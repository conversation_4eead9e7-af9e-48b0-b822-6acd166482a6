"""
وحدة الأدوات المساعدة لنظام إدارة فواتير الكهرباء
"""

import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import re

class ValidationUtils:
    """أدوات التحقق من صحة البيانات"""
    
    @staticmethod
    def validate_account_number(account_no):
        """التحقق من صحة رقم الحساب"""
        if not account_no or len(account_no.strip()) == 0:
            return False, "رقم الحساب مطلوب"
        
        if not re.match(r'^\d+$', account_no.strip()):
            return False, "رقم الحساب يجب أن يحتوي على أرقام فقط"
        
        if len(account_no.strip()) < 4:
            return False, "رقم الحساب يجب أن يكون 4 أرقام على الأقل"
        
        return True, ""
    
    @staticmethod
    def validate_name(name):
        """التحقق من صحة الاسم"""
        if not name or len(name.strip()) == 0:
            return False, "الاسم مطلوب"
        
        if len(name.strip()) < 2:
            return False, "الاسم يجب أن يكون حرفين على الأقل"
        
        return True, ""
    
    @staticmethod
    def validate_number(value, field_name, allow_negative=False):
        """التحقق من صحة الأرقام"""
        if not value or len(str(value).strip()) == 0:
            return True, ""  # الحقول الرقمية اختيارية
        
        try:
            num = float(value)
            if not allow_negative and num < 0:
                return False, f"{field_name} لا يمكن أن يكون سالباً"
            return True, ""
        except ValueError:
            return False, f"{field_name} يجب أن يكون رقماً صحيحاً"
    
    @staticmethod
    def validate_date(date_str):
        """التحقق من صحة التاريخ"""
        if not date_str or len(date_str.strip()) == 0:
            return True, ""  # التاريخ اختياري
        
        try:
            datetime.strptime(date_str, "%Y-%m-%d")
            return True, ""
        except ValueError:
            return False, "تنسيق التاريخ غير صحيح (YYYY-MM-DD)"

class FormatUtils:
    """أدوات التنسيق"""
    
    @staticmethod
    def format_currency(amount):
        """تنسيق المبالغ المالية"""
        if amount is None:
            return "0"
        try:
            return f"{float(amount):,.0f}"
        except (ValueError, TypeError):
            return "0"
    
    @staticmethod
    def format_date(date_str):
        """تنسيق التاريخ"""
        if not date_str:
            return ""
        try:
            date_obj = datetime.strptime(date_str, "%Y-%m-%d")
            return date_obj.strftime("%d/%m/%Y")
        except ValueError:
            return date_str
    
    @staticmethod
    def format_consumption(consumption):
        """تنسيق الاستهلاك"""
        if consumption is None:
            return "0.0"
        try:
            return f"{float(consumption):.1f}"
        except (ValueError, TypeError):
            return "0.0"

class DialogUtils:
    """أدوات النوافذ المنبثقة"""
    
    @staticmethod
    def show_info(title, message):
        """عرض رسالة معلومات"""
        messagebox.showinfo(title, message)
    
    @staticmethod
    def show_warning(title, message):
        """عرض رسالة تحذير"""
        messagebox.showwarning(title, message)
    
    @staticmethod
    def show_error(title, message):
        """عرض رسالة خطأ"""
        messagebox.showerror(title, message)
    
    @staticmethod
    def ask_yes_no(title, message):
        """سؤال نعم/لا"""
        return messagebox.askyesno(title, message)
    
    @staticmethod
    def ask_ok_cancel(title, message):
        """سؤال موافق/إلغاء"""
        return messagebox.askokcancel(title, message)

class WidgetUtils:
    """أدوات الواجهة"""
    
    @staticmethod
    def create_labeled_entry(parent, label_text, row, column=0, width=20, **kwargs):
        """إنشاء حقل إدخال مع تسمية"""
        ttk.Label(parent, text=label_text).grid(
            row=row, column=column, sticky=tk.W, padx=(0, 5), pady=2)
        
        entry = ttk.Entry(parent, width=width, **kwargs)
        entry.grid(row=row, column=column+1, padx=(0, 10), pady=2, sticky=tk.W)
        
        return entry
    
    @staticmethod
    def create_labeled_combobox(parent, label_text, values, row, column=0, width=18, **kwargs):
        """إنشاء قائمة منسدلة مع تسمية"""
        ttk.Label(parent, text=label_text).grid(
            row=row, column=column, sticky=tk.W, padx=(0, 5), pady=2)
        
        combobox = ttk.Combobox(parent, values=values, width=width, state="readonly", **kwargs)
        combobox.grid(row=row, column=column+1, padx=(0, 10), pady=2, sticky=tk.W)
        
        return combobox
    
    @staticmethod
    def create_button_frame(parent, buttons_config):
        """إنشاء إطار أزرار
        buttons_config: قائمة من tuples (text, command, **kwargs)
        """
        frame = ttk.Frame(parent)
        
        for i, config in enumerate(buttons_config):
            text = config[0]
            command = config[1]
            kwargs = config[2] if len(config) > 2 else {}
            
            button = ttk.Button(frame, text=text, command=command, **kwargs)
            button.pack(side=tk.LEFT, padx=5)
        
        return frame
    
    @staticmethod
    def center_window(window, width=None, height=None):
        """توسيط النافذة على الشاشة"""
        window.update_idletasks()
        
        if width is None:
            width = window.winfo_width()
        if height is None:
            height = window.winfo_height()
        
        screen_width = window.winfo_screenwidth()
        screen_height = window.winfo_screenheight()
        
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        
        window.geometry(f"{width}x{height}+{x}+{y}")

class FilterUtils:
    """أدوات الفلترة"""
    
    @staticmethod
    def filter_by_debt_range(subscribers, min_debt=None, max_debt=None):
        """فلترة حسب نطاق الديون"""
        filtered = []
        for subscriber in subscribers:
            debt = (subscriber[17] or 0) - (subscriber[21] or 0)  # OUTS - PAYMENT
            
            if min_debt is not None and debt < min_debt:
                continue
            if max_debt is not None and debt > max_debt:
                continue
            
            filtered.append(subscriber)
        
        return filtered
    
    @staticmethod
    def filter_by_consumption_range(subscribers, min_consumption=None, max_consumption=None):
        """فلترة حسب نطاق الاستهلاك"""
        filtered = []
        for subscriber in subscribers:
            consumption = subscriber[28] or 0  # AV_CONS
            
            if min_consumption is not None and consumption < min_consumption:
                continue
            if max_consumption is not None and consumption > max_consumption:
                continue
            
            filtered.append(subscriber)
        
        return filtered
    
    @staticmethod
    def filter_by_meter_type(subscribers, meter_type):
        """فلترة حسب نوع العداد"""
        if not meter_type or meter_type == "جميع الأنواع":
            return subscribers
        
        filtered = []
        for subscriber in subscribers:
            if subscriber[8] == meter_type:  # MPHASE
                filtered.append(subscriber)
        
        return filtered

class ExportUtils:
    """أدوات التصدير"""
    
    @staticmethod
    def prepare_data_for_export(subscribers):
        """تحضير البيانات للتصدير"""
        headers = [
            'رقم الحساب', 'رقم التركيب', 'الرقم التسلسلي', 'الاسم', 'رقم البيت',
            'العنوان', 'رقم العداد', 'نوع العداد', 'معامل العداد', 'القراءة الأخيرة',
            'تاريخ القراءة الأخيرة', 'القراءة السابقة', 'تاريخ القراءة السابقة',
            'إيجار العداد', 'إيجار القاطع', 'رسوم أخرى', 'المبلغ المستحق', 'المتأخرات',
            'كود البيت', 'مغلق', 'المدفوع', 'تاريخ الدفع', 'تاريخ الفاتورة', 'صرف قديم',
            'علامة عداد سيء', 'علامة احتياطي', 'حساب الفاتورة', 'متوسط الاستهلاك',
            'علامة التشغيل', 'القراءة', 'المنجة', 'اسم العداد', 'المتبقي'
        ]
        
        data = []
        for subscriber in subscribers:
            row = list(subscriber[1:])  # تجاهل ID
            # إضافة المتبقي
            debt = (subscriber[17] or 0) - (subscriber[21] or 0)
            row.append(debt)
            data.append(row)
        
        return headers, data
    
    @staticmethod
    def format_data_for_display(subscribers):
        """تنسيق البيانات للعرض"""
        formatted_data = []
        for subscriber in subscribers:
            debt = (subscriber[17] or 0) - (subscriber[21] or 0)
            formatted_row = (
                subscriber[1],  # ACCTNO
                subscriber[4],  # NAME_A
                subscriber[6],  # ADRESS
                subscriber[7],  # MATER_NO
                FormatUtils.format_consumption(subscriber[10]),  # LAST_READ
                FormatUtils.format_currency(subscriber[17]),     # OUTS
                FormatUtils.format_currency(subscriber[21]),     # PAYMENT
                FormatUtils.format_currency(debt)                # المتبقي
            )
            formatted_data.append(formatted_row)
        
        return formatted_data

class PrintUtils:
    """أدوات الطباعة"""
    
    @staticmethod
    def create_bill_content(subscriber):
        """إنشاء محتوى الفاتورة"""
        debt = (subscriber[17] or 0) - (subscriber[21] or 0)
        
        content = f"""
        ═══════════════════════════════════════════════════════════
                            فاتورة الكهرباء
                        محافظة البصرة - العراق
        ═══════════════════════════════════════════════════════════
        
        رقم الحساب: {subscriber[1]}
        اسم المشترك: {subscriber[4]}
        العنوان: {subscriber[6]}
        رقم العداد: {subscriber[7]}
        
        ───────────────────────────────────────────────────────────
        
        القراءة الأخيرة: {subscriber[10]} كيلو واط
        تاريخ القراءة: {FormatUtils.format_date(subscriber[11])}
        القراءة السابقة: {subscriber[12]} كيلو واط
        الاستهلاك: {(subscriber[10] or 0) - (subscriber[12] or 0)} كيلو واط
        
        ───────────────────────────────────────────────────────────
        
        إيجار العداد: {FormatUtils.format_currency(subscriber[14])} دينار
        إيجار القاطع: {FormatUtils.format_currency(subscriber[15])} دينار
        رسوم أخرى: {FormatUtils.format_currency(subscriber[16])} دينار
        
        المبلغ المستحق: {FormatUtils.format_currency(subscriber[17])} دينار
        المبلغ المدفوع: {FormatUtils.format_currency(subscriber[21])} دينار
        المبلغ المتبقي: {FormatUtils.format_currency(debt)} دينار
        
        ───────────────────────────────────────────────────────────
        
        تاريخ الفاتورة: {FormatUtils.format_date(subscriber[23])}
        تاريخ الطباعة: {datetime.now().strftime("%d/%m/%Y %H:%M")}
        
        ═══════════════════════════════════════════════════════════
        """
        
        return content.strip()

# ثوابت النظام
METER_TYPES = ["1", "3"]  # أحادي الطور، ثلاثي الطور
TRANSACTION_TYPES = [
    "تغيير الاسم",
    "إبدال المقياس", 
    "تغيير الصنف",
    "إغلاق الحساب",
    "إلغاء مبلغ",
    "أخرى"
]

DEBT_RANGES = [
    ("جميع الديون", None, None),
    ("مسدد", None, 0),
    ("ديون منخفضة", 1, 50000),
    ("ديون متوسطة", 50001, 100000),
    ("ديون عالية", 100001, None)
]

CONSUMPTION_RANGES = [
    ("جميع الاستهلاك", None, None),
    ("منخفض (0-50)", 0, 50),
    ("متوسط (51-100)", 51, 100),
    ("عالي (101-150)", 101, 150),
    ("عالي جداً (150+)", 151, None)
]
