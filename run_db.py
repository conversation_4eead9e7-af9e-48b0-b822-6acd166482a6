#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة تشغيل قاعدة بيانات فواتير الكهرباء
Database Runner for Electricity Bills
"""

import sqlite3
import sys
import os

def run_query(query):
    """تشغيل استعلام SQL"""
    try:
        conn = sqlite3.connect("electricity_bills.db")
        cursor = conn.cursor()
        
        if query.strip().upper().startswith('SELECT'):
            # استعلام قراءة
            cursor.execute(query)
            results = cursor.fetchall()
            
            # عرض النتائج
            if results:
                print(f"تم العثور على {len(results)} نتيجة:")
                print("-" * 50)
                for i, row in enumerate(results, 1):
                    print(f"{i}: {row}")
            else:
                print("لا توجد نتائج")
        else:
            # استعلام تعديل
            cursor.execute(query)
            conn.commit()
            print(f"تم تنفيذ الاستعلام بنجاح. تأثر {cursor.rowcount} سجل")
        
        conn.close()
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"خطأ عام: {e}")

def show_menu():
    """عرض القائمة الرئيسية"""
    print("\n" + "=" * 60)
    print("أداة إدارة قاعدة بيانات فواتير الكهرباء")
    print("=" * 60)
    print("1. عرض جميع المشتركين")
    print("2. البحث عن مشترك بالاسم")
    print("3. البحث عن مشترك برقم الحساب")
    print("4. عرض الإحصائيات")
    print("5. عرض المشتركين ذوي الديون العالية")
    print("6. تشغيل استعلام مخصص")
    print("7. عرض هيكل الجداول")
    print("8. خروج")
    print("-" * 60)

def main():
    """الدالة الرئيسية"""
    
    # التحقق من وجود قاعدة البيانات
    if not os.path.exists("electricity_bills.db"):
        print("خطأ: لم يتم العثور على ملف قاعدة البيانات electricity_bills.db")
        return
    
    print("مرحباً بك في أداة إدارة قاعدة بيانات فواتير الكهرباء")
    
    while True:
        show_menu()
        choice = input("اختر العملية المطلوبة (1-8): ").strip()
        
        if choice == "1":
            # عرض جميع المشتركين
            query = "SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers ORDER BY ACCTNO"
            print("\nجميع المشتركين:")
            run_query(query)
        
        elif choice == "2":
            # البحث بالاسم
            name = input("أدخل اسم المشترك: ").strip()
            query = f"SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers WHERE NAME_A LIKE '%{name}%'"
            print(f"\nنتائج البحث عن '{name}':")
            run_query(query)
        
        elif choice == "3":
            # البحث برقم الحساب
            account = input("أدخل رقم الحساب: ").strip()
            query = f"SELECT * FROM subscribers WHERE ACCTNO = '{account}'"
            print(f"\nتفاصيل الحساب رقم {account}:")
            run_query(query)
        
        elif choice == "4":
            # عرض الإحصائيات
            print("\nالإحصائيات:")
            queries = [
                ("عدد المشتركين", "SELECT COUNT(*) as total_subscribers FROM subscribers"),
                ("إجمالي المبالغ المستحقة", "SELECT SUM(OUTS) as total_outstanding FROM subscribers"),
                ("إجمالي المدفوعات", "SELECT SUM(PAYMENT) as total_payments FROM subscribers"),
                ("متوسط المبلغ المستحق", "SELECT AVG(OUTS) as avg_outstanding FROM subscribers WHERE OUTS > 0")
            ]
            
            for desc, query in queries:
                print(f"\n{desc}:")
                run_query(query)
        
        elif choice == "5":
            # المشتركين ذوي الديون العالية
            limit = input("أدخل الحد الأدنى للدين (افتراضي 100000): ").strip()
            limit = limit if limit.isdigit() else "100000"
            query = f"SELECT ACCTNO, NAME_A, OUTS, PAYMENT, (OUTS - PAYMENT) as remaining FROM subscribers WHERE (OUTS - PAYMENT) > {limit} ORDER BY remaining DESC"
            print(f"\nالمشتركين ذوي الديون أكثر من {limit} دينار:")
            run_query(query)
        
        elif choice == "6":
            # استعلام مخصص
            print("\nتشغيل استعلام مخصص:")
            print("مثال: SELECT * FROM subscribers WHERE OUTS > 50000")
            query = input("أدخل الاستعلام: ").strip()
            if query:
                run_query(query)
            else:
                print("لم يتم إدخال استعلام")
        
        elif choice == "7":
            # عرض هيكل الجداول
            print("\nهيكل الجداول:")
            tables = ["subscribers", "transactions"]
            for table in tables:
                print(f"\nجدول {table}:")
                query = f"PRAGMA table_info({table})"
                run_query(query)
        
        elif choice == "8":
            print("شكراً لاستخدام البرنامج!")
            break
        
        else:
            print("خيار غير صحيح، يرجى اختيار رقم من 1 إلى 8")
        
        input("\nاضغط Enter للمتابعة...")

if __name__ == "__main__":
    main()
