"""
ملف لتحويل المشروع إلى ملف تنفيذي باستخدام PyInstaller
"""

import os
import subprocess
import sys

def install_requirements():
    """تثبيت المتطلبات"""
    print("تثبيت المتطلبات...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("تم تثبيت المتطلبات بنجاح")
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت المتطلبات: {e}")
        return False
    return True

def install_pyinstaller():
    """تثبيت PyInstaller"""
    print("تثبيت PyInstaller...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
        print("تم تثبيت PyInstaller بنجاح")
    except subprocess.CalledProcessError as e:
        print(f"خطأ في تثبيت PyInstaller: {e}")
        return False
    return True

def build_executable():
    """بناء الملف التنفيذي"""
    print("بناء الملف التنفيذي...")
    
    # أوامر PyInstaller
    cmd = [
        "pyinstaller",
        "--onefile",                    # ملف واحد
        "--windowed",                   # بدون نافذة الكونسول
        "--name=ElectricityBills",      # اسم الملف التنفيذي
        "--icon=icon.ico",              # أيقونة (اختيارية)
        "--add-data=electricity_bills.db;.",  # إضافة قاعدة البيانات
        "main.py"
    ]
    
    try:
        subprocess.check_call(cmd)
        print("تم بناء الملف التنفيذي بنجاح!")
        print("يمكنك العثور على الملف في مجلد 'dist'")
        return True
    except subprocess.CalledProcessError as e:
        print(f"خطأ في بناء الملف التنفيذي: {e}")
        return False
    except FileNotFoundError:
        print("PyInstaller غير مثبت. يرجى تثبيته أولاً.")
        return False

def create_batch_file():
    """إنشاء ملف batch لتشغيل المشروع"""
    batch_content = """@echo off
echo تشغيل نظام إدارة فواتير الكهرباء...
python main.py
pause
"""
    
    with open("run_app.bat", "w", encoding="utf-8") as f:
        f.write(batch_content)
    
    print("تم إنشاء ملف run_app.bat لتشغيل المشروع")

def main():
    """الدالة الرئيسية"""
    print("=" * 50)
    print("أداة بناء نظام إدارة فواتير الكهرباء")
    print("=" * 50)
    
    # تثبيت المتطلبات
    if not install_requirements():
        return
    
    # إنشاء ملف batch
    create_batch_file()
    
    # سؤال المستخدم عن بناء الملف التنفيذي
    choice = input("\nهل تريد بناء ملف تنفيذي؟ (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', 'نعم']:
        if install_pyinstaller():
            if build_executable():
                print("\n" + "=" * 50)
                print("تم الانتهاء بنجاح!")
                print("يمكنك تشغيل المشروع بإحدى الطرق التالية:")
                print("1. تشغيل الملف التنفيذي من مجلد dist")
                print("2. تشغيل run_app.bat")
                print("3. تشغيل python main.py")
                print("=" * 50)
    else:
        print("\n" + "=" * 50)
        print("يمكنك تشغيل المشروع بإحدى الطرق التالية:")
        print("1. تشغيل run_app.bat")
        print("2. تشغيل python main.py")
        print("=" * 50)

if __name__ == "__main__":
    main()
