import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
from datetime import datetime
import sqlite3

class ReportsManager:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager
        
    def show_reports_window(self):
        """عرض نافذة التقارير"""
        self.reports_window = tk.Toplevel(self.parent)
        self.reports_window.title("التقارير والإحصائيات")
        self.reports_window.geometry("1200x800")
        self.reports_window.configure(bg='#f0f0f0')
        
        self.create_reports_interface()
        self.load_statistics()
    
    def create_reports_interface(self):
        """إنشاء واجهة التقارير"""
        main_frame = ttk.Frame(self.reports_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title = ttk.Label(main_frame, text="التقارير والإحصائيات", 
                         font=('Arial', 16, 'bold'), foreground='#2c3e50')
        title.pack(pady=(0, 20))
        
        # إطار الإحصائيات العامة
        stats_frame = ttk.LabelFrame(main_frame, text="الإحصائيات العامة", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        # إنشاء الإحصائيات
        self.create_statistics_display(stats_frame)
        
        # إطار التقارير
        reports_frame = ttk.LabelFrame(main_frame, text="التقارير المتاحة", padding="10")
        reports_frame.pack(fill=tk.X, pady=(0, 10))
        
        # أزرار التقارير
        buttons_grid = ttk.Frame(reports_frame)
        buttons_grid.pack()
        
        report_buttons = [
            ("تقرير الديون العالية", self.high_debts_report),
            ("تقرير الحسابات الجديدة", self.new_accounts_report),
            ("تقرير الاستهلاك", self.consumption_report),
            ("تقرير المدفوعات", self.payments_report),
            ("تقرير المعاملات", self.transactions_report),
            ("تقرير شامل", self.comprehensive_report)
        ]
        
        for i, (text, command) in enumerate(report_buttons):
            row = i // 3
            col = i % 3
            ttk.Button(buttons_grid, text=text, command=command, width=20).grid(
                row=row, column=col, padx=5, pady=5)
        
        # إطار الرسوم البيانية
        charts_frame = ttk.LabelFrame(main_frame, text="الرسوم البيانية", padding="10")
        charts_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))
        
        # إنشاء الرسوم البيانية
        self.create_charts(charts_frame)
        
        # أزرار الإغلاق والتصدير
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.pack(pady=(10, 0))
        
        ttk.Button(bottom_frame, text="تصدير جميع التقارير", 
                  command=self.export_all_reports).pack(side=tk.LEFT, padx=5)
        ttk.Button(bottom_frame, text="إغلاق", 
                  command=self.reports_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def create_statistics_display(self, parent):
        """إنشاء عرض الإحصائيات"""
        # إطار الإحصائيات
        stats_grid = ttk.Frame(parent)
        stats_grid.pack()
        
        # متغيرات الإحصائيات
        self.total_subscribers = tk.StringVar()
        self.total_outstanding = tk.StringVar()
        self.total_payments = tk.StringVar()
        self.avg_consumption = tk.StringVar()
        self.high_debt_count = tk.StringVar()
        self.new_accounts_count = tk.StringVar()
        
        # عرض الإحصائيات
        stats_data = [
            ("إجمالي المشتركين", self.total_subscribers),
            ("إجمالي المبالغ المستحقة", self.total_outstanding),
            ("إجمالي المدفوعات", self.total_payments),
            ("متوسط الاستهلاك", self.avg_consumption),
            ("الحسابات عالية الديون", self.high_debt_count),
            ("الحسابات الجديدة", self.new_accounts_count)
        ]
        
        for i, (label, var) in enumerate(stats_data):
            row = i // 3
            col = (i % 3) * 2
            
            ttk.Label(stats_grid, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 5), pady=5)
            ttk.Label(stats_grid, textvariable=var, font=('Arial', 10), 
                     foreground='#e74c3c').grid(
                row=row, column=col+1, sticky=tk.W, padx=(0, 20), pady=5)
    
    def load_statistics(self):
        """تحميل الإحصائيات"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # إجمالي المشتركين
            cursor.execute("SELECT COUNT(*) FROM subscribers")
            total_subs = cursor.fetchone()[0]
            self.total_subscribers.set(f"{total_subs:,}")
            
            # إجمالي المبالغ المستحقة
            cursor.execute("SELECT SUM(OUTS) FROM subscribers WHERE OUTS IS NOT NULL")
            total_outs = cursor.fetchone()[0] or 0
            self.total_outstanding.set(f"{total_outs:,.0f} دينار")
            
            # إجمالي المدفوعات
            cursor.execute("SELECT SUM(PAYMENT) FROM subscribers WHERE PAYMENT IS NOT NULL")
            total_payments = cursor.fetchone()[0] or 0
            self.total_payments.set(f"{total_payments:,.0f} دينار")
            
            # متوسط الاستهلاك
            cursor.execute("SELECT AVG(AV_CONS) FROM subscribers WHERE AV_CONS IS NOT NULL")
            avg_cons = cursor.fetchone()[0] or 0
            self.avg_consumption.set(f"{avg_cons:.1f} كيلو واط")
            
            # الحسابات عالية الديون (أكثر من 100,000)
            cursor.execute("SELECT COUNT(*) FROM subscribers WHERE (OUTS - PAYMENT) > 100000")
            high_debt = cursor.fetchone()[0]
            self.high_debt_count.set(f"{high_debt:,}")
            
            # الحسابات الجديدة (آخر 30 يوم)
            cursor.execute("""
                SELECT COUNT(*) FROM subscribers 
                WHERE created_date >= date('now', '-30 days')
            """)
            new_accounts = cursor.fetchone()[0]
            self.new_accounts_count.set(f"{new_accounts:,}")
            
            conn.close()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في تحميل الإحصائيات:\n{str(e)}")
    
    def create_charts(self, parent):
        """إنشاء الرسوم البيانية"""
        # إطار الرسوم البيانية
        charts_notebook = ttk.Notebook(parent)
        charts_notebook.pack(fill=tk.BOTH, expand=True)
        
        # رسم بياني للديون
        self.create_debts_chart(charts_notebook)
        
        # رسم بياني للاستهلاك
        self.create_consumption_chart(charts_notebook)
        
        # رسم بياني للمدفوعات
        self.create_payments_chart(charts_notebook)
    
    def create_debts_chart(self, notebook):
        """إنشاء رسم بياني للديون"""
        debts_frame = ttk.Frame(notebook)
        notebook.add(debts_frame, text="توزيع الديون")
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # جلب بيانات الديون
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN (OUTS - PAYMENT) <= 0 THEN 'مسدد'
                        WHEN (OUTS - PAYMENT) <= 50000 THEN 'ديون منخفضة'
                        WHEN (OUTS - PAYMENT) <= 100000 THEN 'ديون متوسطة'
                        ELSE 'ديون عالية'
                    END as debt_category,
                    COUNT(*) as count
                FROM subscribers 
                GROUP BY debt_category
            """)
            
            data = cursor.fetchall()
            conn.close()
            
            if data:
                categories = [row[0] for row in data]
                counts = [row[1] for row in data]
                
                fig, ax = plt.subplots(figsize=(8, 6))
                colors = ['#2ecc71', '#f39c12', '#e67e22', '#e74c3c']
                ax.pie(counts, labels=categories, autopct='%1.1f%%', colors=colors[:len(categories)])
                ax.set_title('توزيع المشتركين حسب الديون', fontsize=14, fontweight='bold')
                
                canvas = FigureCanvasTkAgg(fig, debts_frame)
                canvas.draw()
                canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
                
        except Exception as e:
            ttk.Label(debts_frame, text=f"خطأ في تحميل البيانات: {str(e)}").pack()
    
    def create_consumption_chart(self, notebook):
        """إنشاء رسم بياني للاستهلاك"""
        consumption_frame = ttk.Frame(notebook)
        notebook.add(consumption_frame, text="الاستهلاك")
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # جلب بيانات الاستهلاك
            cursor.execute("""
                SELECT 
                    CASE 
                        WHEN AV_CONS <= 50 THEN '0-50 كيلو واط'
                        WHEN AV_CONS <= 100 THEN '51-100 كيلو واط'
                        WHEN AV_CONS <= 150 THEN '101-150 كيلو واط'
                        ELSE 'أكثر من 150 كيلو واط'
                    END as consumption_range,
                    COUNT(*) as count
                FROM subscribers 
                WHERE AV_CONS IS NOT NULL
                GROUP BY consumption_range
            """)
            
            data = cursor.fetchall()
            conn.close()
            
            if data:
                ranges = [row[0] for row in data]
                counts = [row[1] for row in data]
                
                fig, ax = plt.subplots(figsize=(8, 6))
                bars = ax.bar(ranges, counts, color=['#3498db', '#9b59b6', '#e67e22', '#e74c3c'])
                ax.set_title('توزيع المشتركين حسب الاستهلاك', fontsize=14, fontweight='bold')
                ax.set_ylabel('عدد المشتركين')
                ax.set_xlabel('نطاق الاستهلاك')
                
                # إضافة القيم على الأعمدة
                for bar in bars:
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height,
                           f'{int(height)}', ha='center', va='bottom')
                
                plt.xticks(rotation=45)
                plt.tight_layout()
                
                canvas = FigureCanvasTkAgg(fig, consumption_frame)
                canvas.draw()
                canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
                
        except Exception as e:
            ttk.Label(consumption_frame, text=f"خطأ في تحميل البيانات: {str(e)}").pack()
    
    def create_payments_chart(self, notebook):
        """إنشاء رسم بياني للمدفوعات"""
        payments_frame = ttk.Frame(notebook)
        notebook.add(payments_frame, text="المدفوعات")
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            # جلب بيانات المدفوعات الشهرية (محاكاة)
            months = ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو']
            payments = [1500000, 1800000, 1600000, 1900000, 2100000, 1750000]
            
            fig, ax = plt.subplots(figsize=(8, 6))
            line = ax.plot(months, payments, marker='o', linewidth=2, markersize=8, color='#27ae60')
            ax.set_title('المدفوعات الشهرية', fontsize=14, fontweight='bold')
            ax.set_ylabel('المبلغ (دينار)')
            ax.set_xlabel('الشهر')
            ax.grid(True, alpha=0.3)
            
            # تنسيق المحور Y
            ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'{x/1000000:.1f}M'))
            
            plt.xticks(rotation=45)
            plt.tight_layout()
            
            canvas = FigureCanvasTkAgg(fig, payments_frame)
            canvas.draw()
            canvas.get_tk_widget().pack(fill=tk.BOTH, expand=True)
            
            conn.close()
            
        except Exception as e:
            ttk.Label(payments_frame, text=f"خطأ في تحميل البيانات: {str(e)}").pack()
    
    def high_debts_report(self):
        """تقرير الديون العالية"""
        self.generate_report("الديون العالية", """
            SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT, (OUTS - PAYMENT) as debt
            FROM subscribers 
            WHERE (OUTS - PAYMENT) > 100000
            ORDER BY debt DESC
        """, ['رقم الحساب', 'الاسم', 'العنوان', 'المستحق', 'المدفوع', 'الدين'])
    
    def new_accounts_report(self):
        """تقرير الحسابات الجديدة"""
        self.generate_report("الحسابات الجديدة", """
            SELECT ACCTNO, NAME_A, ADRESS, created_date
            FROM subscribers 
            WHERE created_date >= date('now', '-30 days')
            ORDER BY created_date DESC
        """, ['رقم الحساب', 'الاسم', 'العنوان', 'تاريخ الإنشاء'])
    
    def consumption_report(self):
        """تقرير الاستهلاك"""
        self.generate_report("تقرير الاستهلاك", """
            SELECT ACCTNO, NAME_A, AV_CONS, LAST_READ, PREV_READ, (LAST_READ - PREV_READ) as consumption
            FROM subscribers 
            WHERE AV_CONS IS NOT NULL
            ORDER BY AV_CONS DESC
        """, ['رقم الحساب', 'الاسم', 'متوسط الاستهلاك', 'القراءة الأخيرة', 'القراءة السابقة', 'الاستهلاك'])
    
    def payments_report(self):
        """تقرير المدفوعات"""
        self.generate_report("تقرير المدفوعات", """
            SELECT ACCTNO, NAME_A, PAYMENT, PAY_DATE, OUTS, (OUTS - PAYMENT) as remaining
            FROM subscribers 
            WHERE PAYMENT IS NOT NULL AND PAYMENT > 0
            ORDER BY PAYMENT DESC
        """, ['رقم الحساب', 'الاسم', 'المدفوع', 'تاريخ الدفع', 'المستحق', 'المتبقي'])
    
    def transactions_report(self):
        """تقرير المعاملات"""
        self.generate_report("تقرير المعاملات", """
            SELECT ACCTNO, transaction_type, old_value, new_value, transaction_date
            FROM transactions 
            ORDER BY transaction_date DESC
        """, ['رقم الحساب', 'نوع المعاملة', 'القيمة القديمة', 'القيمة الجديدة', 'التاريخ'])
    
    def comprehensive_report(self):
        """التقرير الشامل"""
        self.generate_report("التقرير الشامل", """
            SELECT ACCTNO, NAME_A, ADRESS, MATER_NO, LAST_READ, OUTS, PAYMENT, 
                   (OUTS - PAYMENT) as debt, AV_CONS
            FROM subscribers 
            ORDER BY ACCTNO
        """, ['رقم الحساب', 'الاسم', 'العنوان', 'رقم العداد', 'القراءة', 'المستحق', 'المدفوع', 'الدين', 'الاستهلاك'])
    
    def generate_report(self, title, query, columns):
        """إنتاج تقرير"""
        try:
            conn = sqlite3.connect(self.db.db_path)
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            if df.empty:
                messagebox.showinfo("تنبيه", "لا توجد بيانات لعرضها في هذا التقرير")
                return
            
            # إنشاء نافذة التقرير
            report_window = tk.Toplevel(self.reports_window)
            report_window.title(f"تقرير {title}")
            report_window.geometry("1000x600")
            
            main_frame = ttk.Frame(report_window, padding="10")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # العنوان
            ttk.Label(main_frame, text=f"تقرير {title}", 
                     font=('Arial', 16, 'bold')).pack(pady=(0, 10))
            
            # الجدول
            tree_frame = ttk.Frame(main_frame)
            tree_frame.pack(fill=tk.BOTH, expand=True)
            
            tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
            
            for col in columns:
                tree.heading(col, text=col)
                tree.column(col, width=100, anchor='center')
            
            # إضافة البيانات
            for _, row in df.iterrows():
                tree.insert('', 'end', values=list(row))
            
            # شريط التمرير
            scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=tree.yview)
            tree.configure(yscrollcommand=scrollbar.set)
            
            tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # أزرار التصدير
            buttons_frame = ttk.Frame(main_frame)
            buttons_frame.pack(pady=(10, 0))
            
            ttk.Button(buttons_frame, text="تصدير Excel", 
                      command=lambda: self.export_dataframe(df, title)).pack(side=tk.LEFT, padx=5)
            ttk.Button(buttons_frame, text="إغلاق", 
                      command=report_window.destroy).pack(side=tk.LEFT, padx=5)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ في إنتاج التقرير:\n{str(e)}")
    
    def export_dataframe(self, df, title):
        """تصدير DataFrame إلى Excel"""
        try:
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx")],
                title=f"حفظ تقرير {title}"
            )
            
            if filename:
                df.to_excel(filename, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير التقرير إلى:\n{filename}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
    
    def export_all_reports(self):
        """تصدير جميع التقارير"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
