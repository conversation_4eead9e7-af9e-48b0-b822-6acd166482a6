import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import sqlite3
from database import DatabaseManager
from transactions import TransactionsManager
from reports import ReportsManager
from subscriber_manager import SubscriberManager
from datetime import datetime
import pandas as pd
from reportlab.lib.pagesizes import letter, A4
from reportlab.pdfgen import canvas
from reportlab.lib.units import inch
from reportlab.pdfbase import pdfmetrics
from reportlab.pdfbase.ttfonts import TTFont
import os

class ElectricityBillsApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة فواتير الكهرباء - البصرة")
        self.root.geometry("1400x800")
        self.root.configure(bg='#f0f0f0')
        
        # إعداد قاعدة البيانات
        self.db = DatabaseManager()

        # إعداد مدراء الوحدات
        self.transactions_manager = TransactionsManager(self.root, self.db)
        self.reports_manager = ReportsManager(self.root, self.db)
        self.subscriber_manager = SubscriberManager(self, self.db)

        # إعداد الثيم
        self.setup_theme()

        # إنشاء الواجهة الرئيسية
        self.create_main_interface()

        # تحميل البيانات
        self.load_data()
    
    def setup_theme(self):
        """إعداد الثيم والألوان"""
        style = ttk.Style()
        style.theme_use('clam')
        
        # ألوان مخصصة
        style.configure('Title.TLabel', font=('Arial', 16, 'bold'), foreground='#2c3e50')
        style.configure('Header.TLabel', font=('Arial', 12, 'bold'), foreground='#34495e')
        style.configure('Custom.TButton', font=('Arial', 10, 'bold'))
        style.configure('Search.TEntry', font=('Arial', 11))
        
        # ألوان الجدول
        style.configure("Treeview", background="#ffffff", foreground="#2c3e50", 
                       rowheight=25, fieldbackground="#ffffff")
        style.configure("Treeview.Heading", font=('Arial', 10, 'bold'), 
                       background="#3498db", foreground="white")
        style.map("Treeview", background=[('selected', '#3498db')])
    
    def create_main_interface(self):
        """إنشاء الواجهة الرئيسية"""
        # الإطار الرئيسي
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # العنوان الرئيسي
        title_label = ttk.Label(main_frame, text="نظام إدارة فواتير الكهرباء - البصرة", 
                               style='Title.TLabel')
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20))
        
        # إطار البحث
        search_frame = ttk.LabelFrame(main_frame, text="البحث والاستعلام", padding="10")
        search_frame.grid(row=1, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # خيارات البحث
        ttk.Label(search_frame, text="نوع البحث:").grid(row=0, column=0, padx=(0, 5))
        self.search_type = ttk.Combobox(search_frame, values=["الاسم", "رقم الحساب", "العنوان"], 
                                       state="readonly", width=15)
        self.search_type.set("الاسم")
        self.search_type.grid(row=0, column=1, padx=(0, 10))
        
        ttk.Label(search_frame, text="البحث:").grid(row=0, column=2, padx=(0, 5))
        self.search_entry = ttk.Entry(search_frame, style='Search.TEntry', width=30)
        self.search_entry.grid(row=0, column=3, padx=(0, 10))
        self.search_entry.bind('<KeyRelease>', self.on_search)
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(search_frame)
        buttons_frame.grid(row=0, column=4, padx=(10, 0))
        
        ttk.Button(buttons_frame, text="بحث", command=self.search_data, 
                  style='Custom.TButton').grid(row=0, column=0, padx=2)
        ttk.Button(buttons_frame, text="إضافة مشترك", command=self.add_subscriber, 
                  style='Custom.TButton').grid(row=0, column=1, padx=2)
        ttk.Button(buttons_frame, text="تعديل", command=self.edit_subscriber, 
                  style='Custom.TButton').grid(row=0, column=2, padx=2)
        ttk.Button(buttons_frame, text="حذف", command=self.delete_subscriber, 
                  style='Custom.TButton').grid(row=0, column=3, padx=2)
        
        # الجدول الرئيسي
        self.create_treeview(main_frame)
        
        # إطار الأزرار السفلي
        bottom_frame = ttk.Frame(main_frame)
        bottom_frame.grid(row=3, column=0, columnspan=4, pady=(10, 0), sticky=(tk.W, tk.E))
        
        # أزرار التقارير والعمليات
        ttk.Button(bottom_frame, text="عرض التفاصيل", command=self.show_details, 
                  style='Custom.TButton').grid(row=0, column=0, padx=5)
        ttk.Button(bottom_frame, text="المعاملات", command=self.show_transactions, 
                  style='Custom.TButton').grid(row=0, column=1, padx=5)
        ttk.Button(bottom_frame, text="التقارير", command=self.show_reports, 
                  style='Custom.TButton').grid(row=0, column=2, padx=5)
        ttk.Button(bottom_frame, text="طباعة", command=self.print_data, 
                  style='Custom.TButton').grid(row=0, column=3, padx=5)
        ttk.Button(bottom_frame, text="تصدير Excel", command=self.export_excel, 
                  style='Custom.TButton').grid(row=0, column=4, padx=5)
        
        # شريط الحالة
        self.status_var = tk.StringVar()
        self.status_var.set("جاهز")
        status_bar = ttk.Label(main_frame, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=4, column=0, columnspan=4, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # تكوين الشبكة
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
    
    def create_treeview(self, parent):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        tree_frame = ttk.Frame(parent)
        tree_frame.grid(row=2, column=0, columnspan=4, sticky=(tk.W, tk.E, tk.N, tk.S), pady=10)
        
        # الجدول
        columns = ('رقم الحساب', 'الاسم', 'العنوان', 'رقم العداد', 'القراءة الأخيرة', 
                  'المبلغ المستحق', 'المدفوع', 'المتبقي')
        
        self.tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.tree.xview)
        self.tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # ترتيب العناصر
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        # تكوين الشبكة
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
    
    def load_data(self):
        """تحميل البيانات في الجدول"""
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # جلب البيانات من قاعدة البيانات
        subscribers = self.db.search_subscribers()
        
        for subscriber in subscribers:
            # حساب المتبقي
            outstanding = subscriber[17] - subscriber[21]  # OUTS - PAYMENT
            
            self.tree.insert('', 'end', values=(
                subscriber[1],   # ACCTNO
                subscriber[4],   # NAME_A
                subscriber[6],   # ADRESS
                subscriber[7],   # MATER_NO
                subscriber[10],  # LAST_READ
                subscriber[17],  # OUTS
                subscriber[21],  # PAYMENT
                outstanding      # المتبقي
            ))
        
        self.status_var.set(f"تم تحميل {len(subscribers)} مشترك")
    
    def on_search(self, event):
        """البحث التلقائي عند الكتابة"""
        self.search_data()
    
    def search_data(self):
        """البحث في البيانات"""
        search_term = self.search_entry.get()
        search_type_map = {"الاسم": "name", "رقم الحساب": "account", "العنوان": "address"}
        search_type = search_type_map.get(self.search_type.get(), "name")
        
        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        # البحث
        subscribers = self.db.search_subscribers(search_term, search_type)
        
        for subscriber in subscribers:
            outstanding = subscriber[17] - subscriber[21]
            self.tree.insert('', 'end', values=(
                subscriber[1], subscriber[4], subscriber[6], subscriber[7],
                subscriber[10], subscriber[17], subscriber[21], outstanding
            ))
        
        self.status_var.set(f"تم العثور على {len(subscribers)} نتيجة")
    
    def on_item_double_click(self, event):
        """عند النقر المزدوج على عنصر"""
        self.show_details()
    
    def show_details(self):
        """عرض تفاصيل المشترك"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشترك أولاً")
            return
        
        item = self.tree.item(selection[0])
        account_no = item['values'][0]
        
        # فتح نافذة التفاصيل
        self.open_details_window(account_no)
    
    def open_details_window(self, account_no):
        """فتح نافذة تفاصيل المشترك"""
        details_window = tk.Toplevel(self.root)
        details_window.title(f"تفاصيل المشترك - {account_no}")
        details_window.geometry("800x600")
        details_window.configure(bg='#f0f0f0')
        
        # جلب بيانات المشترك
        subscriber = self.db.get_subscriber_by_account(account_no)
        if not subscriber:
            messagebox.showerror("خطأ", "لم يتم العثور على المشترك")
            details_window.destroy()
            return
        
        # إنشاء واجهة التفاصيل
        self.create_details_interface(details_window, subscriber)
    
    def create_details_interface(self, window, subscriber):
        """إنشاء واجهة تفاصيل المشترك"""
        main_frame = ttk.Frame(window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title = ttk.Label(main_frame, text=f"تفاصيل المشترك: {subscriber[4]}", 
                         style='Title.TLabel')
        title.pack(pady=(0, 20))
        
        # إطار البيانات الأساسية
        basic_frame = ttk.LabelFrame(main_frame, text="البيانات الأساسية", padding="10")
        basic_frame.pack(fill=tk.X, pady=(0, 10))
        
        # عرض البيانات في شبكة
        fields = [
            ("رقم الحساب", subscriber[1]), ("رقم التركيب", subscriber[2]),
            ("الرقم التسلسلي", subscriber[3]), ("الاسم", subscriber[4]),
            ("رقم البيت", subscriber[5]), ("العنوان", subscriber[6]),
            ("رقم العداد", subscriber[7]), ("نوع العداد", subscriber[8])
        ]
        
        for i, (label, value) in enumerate(fields):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(basic_frame, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 5), pady=2)
            ttk.Label(basic_frame, text=str(value or ""), font=('Arial', 10)).grid(
                row=row, column=col+1, sticky=tk.W, padx=(0, 20), pady=2)
        
        # إطار القراءات والفواتير
        billing_frame = ttk.LabelFrame(main_frame, text="القراءات والفواتير", padding="10")
        billing_frame.pack(fill=tk.X, pady=(0, 10))
        
        billing_fields = [
            ("القراءة الأخيرة", subscriber[10]), ("تاريخ القراءة الأخيرة", subscriber[11]),
            ("القراءة السابقة", subscriber[12]), ("تاريخ القراءة السابقة", subscriber[13]),
            ("إيجار العداد", subscriber[14]), ("إيجار القاطع", subscriber[15]),
            ("رسوم أخرى", subscriber[16]), ("المبلغ المستحق", subscriber[17]),
            ("المدفوع", subscriber[21]), ("تاريخ الدفع", subscriber[22])
        ]
        
        for i, (label, value) in enumerate(billing_fields):
            row = i // 2
            col = (i % 2) * 2
            ttk.Label(billing_frame, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=row, column=col, sticky=tk.W, padx=(0, 5), pady=2)
            ttk.Label(billing_frame, text=str(value or ""), font=('Arial', 10)).grid(
                row=row, column=col+1, sticky=tk.W, padx=(0, 20), pady=2)
        
        # حساب المتبقي
        outstanding = (subscriber[17] or 0) - (subscriber[21] or 0)
        outstanding_frame = ttk.Frame(billing_frame)
        outstanding_frame.grid(row=len(billing_fields)//2, column=0, columnspan=4, pady=(10, 0))
        
        ttk.Label(outstanding_frame, text="المبلغ المتبقي:", 
                 font=('Arial', 12, 'bold'), foreground='red').pack(side=tk.LEFT)
        ttk.Label(outstanding_frame, text=f"{outstanding:,.0f} دينار", 
                 font=('Arial', 12, 'bold'), foreground='red').pack(side=tk.LEFT, padx=(10, 0))
        
        # أزرار العمليات
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=(20, 0))
        
        ttk.Button(buttons_frame, text="تعديل البيانات", 
                  command=lambda: self.edit_subscriber_details(subscriber)).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إضافة معاملة", 
                  command=lambda: self.add_transaction_window(subscriber[1])).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="طباعة الفاتورة", 
                  command=lambda: self.print_bill(subscriber)).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق", 
                  command=window.destroy).pack(side=tk.LEFT, padx=5)
    
    def add_subscriber(self):
        """إضافة مشترك جديد"""
        self.subscriber_manager.show_add_subscriber_window()

    def edit_subscriber(self):
        """تعديل مشترك"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشترك أولاً")
            return

        item = self.tree.item(selection[0])
        account_no = item['values'][0]
        self.subscriber_manager.show_edit_subscriber_window(account_no)
    
    def delete_subscriber(self):
        """حذف مشترك"""
        selection = self.tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار مشترك أولاً")
            return
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def show_transactions(self):
        """عرض المعاملات"""
        selection = self.tree.selection()
        account_no = None
        if selection:
            item = self.tree.item(selection[0])
            account_no = item['values'][0]

        self.transactions_manager.show_transactions_window(account_no)

    def show_reports(self):
        """عرض التقارير"""
        self.reports_manager.show_reports_window()
    
    def print_data(self):
        """طباعة البيانات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def export_excel(self):
        """تصدير إلى Excel"""
        try:
            # جلب البيانات
            subscribers = self.db.search_subscribers()
            
            # تحويل إلى DataFrame
            df = pd.DataFrame(subscribers, columns=[
                'ID', 'رقم الحساب', 'رقم التركيب', 'الرقم التسلسلي', 'الاسم', 'رقم البيت',
                'العنوان', 'رقم العداد', 'نوع العداد', 'معامل العداد', 'القراءة الأخيرة',
                'تاريخ القراءة الأخيرة', 'القراءة السابقة', 'تاريخ القراءة السابقة',
                'إيجار العداد', 'إيجار القاطع', 'رسوم أخرى', 'المبلغ المستحق', 'المتأخرات',
                'كود البيت', 'مغلق', 'المدفوع', 'تاريخ الدفع', 'تاريخ الفاتورة', 'صرف قديم',
                'علامة عداد سيء', 'علامة احتياطي', 'حساب الفاتورة', 'متوسط الاستهلاك',
                'علامة التشغيل', 'القراءة', 'المنجة', 'اسم العداد', 'تاريخ الإنشاء'
            ])
            
            # حذف العمود الأول (ID)
            df = df.drop('ID', axis=1)
            
            # اختيار مكان الحفظ
            filename = filedialog.asksaveasfilename(
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")],
                title="حفظ الملف"
            )
            
            if filename:
                df.to_excel(filename, index=False, engine='openpyxl')
                messagebox.showinfo("نجح", f"تم تصدير البيانات إلى:\n{filename}")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التصدير:\n{str(e)}")
    
    def edit_subscriber_details(self, subscriber):
        """تعديل تفاصيل المشترك"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def add_transaction_window(self, account_no):
        """نافذة إضافة معاملة"""
        self.transactions_manager.show_transaction_form(account_no)
    
    def print_bill(self, subscriber):
        """طباعة الفاتورة"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")

def main():
    root = tk.Tk()
    app = ElectricityBillsApp(root)
    root.mainloop()

if __name__ == "__main__":
    main()
