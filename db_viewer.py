#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
أداة عرض قاعدة بيانات فواتير الكهرباء
Database Viewer for Electricity Bills
"""

import sqlite3
import pandas as pd
from datetime import datetime

class DatabaseViewer:
    def __init__(self, db_path="electricity_bills.db"):
        self.db_path = db_path
    
    def connect(self):
        """الاتصال بقاعدة البيانات"""
        try:
            conn = sqlite3.connect(self.db_path)
            return conn
        except sqlite3.Error as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {e}")
            return None
    
    def show_tables(self):
        """عرض جميع الجداول في قاعدة البيانات"""
        conn = self.connect()
        if not conn:
            return
        
        cursor = conn.cursor()
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("=" * 50)
        print("الجداول الموجودة في قاعدة البيانات:")
        print("=" * 50)
        for table in tables:
            print(f"- {table[0]}")
        
        conn.close()
    
    def show_table_structure(self, table_name):
        """عرض هيكل الجدول"""
        conn = self.connect()
        if not conn:
            return
        
        cursor = conn.cursor()
        cursor.execute(f"PRAGMA table_info({table_name});")
        columns = cursor.fetchall()
        
        print(f"\n{'=' * 50}")
        print(f"هيكل جدول {table_name}:")
        print(f"{'=' * 50}")
        print(f"{'الرقم':<5} {'اسم العمود':<20} {'النوع':<15} {'مطلوب':<10}")
        print("-" * 50)
        
        for col in columns:
            print(f"{col[0]:<5} {col[1]:<20} {col[2]:<15} {'نعم' if col[3] else 'لا':<10}")
        
        conn.close()
    
    def show_table_data(self, table_name, limit=10):
        """عرض بيانات الجدول"""
        conn = self.connect()
        if not conn:
            return
        
        try:
            # جلب البيانات
            df = pd.read_sql_query(f"SELECT * FROM {table_name} LIMIT {limit}", conn)
            
            print(f"\n{'=' * 80}")
            print(f"بيانات جدول {table_name} (أول {limit} سجل):")
            print(f"{'=' * 80}")
            
            if df.empty:
                print("الجدول فارغ")
            else:
                # عرض البيانات بطريقة منظمة
                pd.set_option('display.max_columns', None)
                pd.set_option('display.width', None)
                pd.set_option('display.max_colwidth', 20)
                print(df.to_string(index=False))
            
        except Exception as e:
            print(f"خطأ في قراءة البيانات: {e}")
        
        conn.close()
    
    def count_records(self, table_name):
        """عد السجلات في الجدول"""
        conn = self.connect()
        if not conn:
            return 0
        
        cursor = conn.cursor()
        cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
        count = cursor.fetchone()[0]
        conn.close()
        
        return count
    
    def show_statistics(self):
        """عرض إحصائيات قاعدة البيانات"""
        conn = self.connect()
        if not conn:
            return
        
        print(f"\n{'=' * 50}")
        print("إحصائيات قاعدة البيانات:")
        print(f"{'=' * 50}")
        
        # إحصائيات المشتركين
        try:
            cursor = conn.cursor()
            
            # عدد المشتركين
            cursor.execute("SELECT COUNT(*) FROM subscribers")
            total_subscribers = cursor.fetchone()[0]
            print(f"إجمالي المشتركين: {total_subscribers:,}")
            
            # إجمالي المبالغ المستحقة
            cursor.execute("SELECT SUM(OUTS) FROM subscribers WHERE OUTS IS NOT NULL")
            total_outs = cursor.fetchone()[0] or 0
            print(f"إجمالي المبالغ المستحقة: {total_outs:,.0f} دينار")
            
            # إجمالي المدفوعات
            cursor.execute("SELECT SUM(PAYMENT) FROM subscribers WHERE PAYMENT IS NOT NULL")
            total_payments = cursor.fetchone()[0] or 0
            print(f"إجمالي المدفوعات: {total_payments:,.0f} دينار")
            
            # المبلغ المتبقي
            remaining = total_outs - total_payments
            print(f"إجمالي المبالغ المتبقية: {remaining:,.0f} دينار")
            
            # عدد المعاملات
            cursor.execute("SELECT COUNT(*) FROM transactions")
            total_transactions = cursor.fetchone()[0]
            print(f"إجمالي المعاملات: {total_transactions:,}")
            
        except Exception as e:
            print(f"خطأ في حساب الإحصائيات: {e}")
        
        conn.close()
    
    def search_subscriber(self, search_term, search_type="name"):
        """البحث عن مشترك"""
        conn = self.connect()
        if not conn:
            return
        
        try:
            if search_type == "name":
                query = "SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers WHERE NAME_A LIKE ? LIMIT 10"
            elif search_type == "account":
                query = "SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers WHERE ACCTNO LIKE ? LIMIT 10"
            else:
                query = "SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers WHERE ADRESS LIKE ? LIMIT 10"
            
            df = pd.read_sql_query(query, conn, params=[f"%{search_term}%"])
            
            print(f"\n{'=' * 80}")
            print(f"نتائج البحث عن '{search_term}':")
            print(f"{'=' * 80}")
            
            if df.empty:
                print("لم يتم العثور على نتائج")
            else:
                print(df.to_string(index=False))
            
        except Exception as e:
            print(f"خطأ في البحث: {e}")
        
        conn.close()

def main():
    """الدالة الرئيسية"""
    viewer = DatabaseViewer()
    
    print("مرحباً بك في عارض قاعدة بيانات فواتير الكهرباء")
    print("=" * 60)
    
    while True:
        print("\nاختر العملية المطلوبة:")
        print("1. عرض الجداول")
        print("2. عرض هيكل جدول")
        print("3. عرض بيانات جدول")
        print("4. عرض الإحصائيات")
        print("5. البحث عن مشترك")
        print("6. خروج")
        
        choice = input("\nأدخل رقم الخيار: ").strip()
        
        if choice == "1":
            viewer.show_tables()
        
        elif choice == "2":
            table_name = input("أدخل اسم الجدول: ").strip()
            viewer.show_table_structure(table_name)
        
        elif choice == "3":
            table_name = input("أدخل اسم الجدول: ").strip()
            limit = input("عدد السجلات المطلوب عرضها (افتراضي 10): ").strip()
            limit = int(limit) if limit.isdigit() else 10
            viewer.show_table_data(table_name, limit)
        
        elif choice == "4":
            viewer.show_statistics()
        
        elif choice == "5":
            search_term = input("أدخل كلمة البحث: ").strip()
            search_type = input("نوع البحث (name/account/address): ").strip().lower()
            if search_type not in ["name", "account", "address"]:
                search_type = "name"
            viewer.search_subscriber(search_term, search_type)
        
        elif choice == "6":
            print("شكراً لاستخدام البرنامج!")
            break
        
        else:
            print("خيار غير صحيح، يرجى المحاولة مرة أخرى")

if __name__ == "__main__":
    main()
