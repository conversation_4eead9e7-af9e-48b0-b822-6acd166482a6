import tkinter as tk
from tkinter import ttk, messagebox
import sqlite3
from datetime import datetime
from utils import ValidationUtils, WidgetUtils, DialogUtils, FormatUtils

class SubscriberManager:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager
    
    def show_add_subscriber_window(self):
        """عرض نافذة إضافة مشترك جديد"""
        self.show_subscriber_form()
    
    def show_edit_subscriber_window(self, account_no):
        """عرض نافذة تعديل مشترك"""
        subscriber = self.db.get_subscriber_by_account(account_no)
        if not subscriber:
            DialogUtils.show_error("خطأ", "لم يتم العثور على المشترك")
            return
        
        self.show_subscriber_form(subscriber)
    
    def show_subscriber_form(self, subscriber=None):
        """عرض نموذج المشترك"""
        is_edit = subscriber is not None
        title = "تعديل مشترك" if is_edit else "إضافة مشترك جديد"
        
        # إنشاء النافذة
        self.form_window = tk.Toplevel(self.parent)
        self.form_window.title(title)
        self.form_window.geometry("800x700")
        self.form_window.configure(bg='#f0f0f0')
        
        # توسيط النافذة
        WidgetUtils.center_window(self.form_window, 800, 700)
        
        # إنشاء الواجهة
        self.create_subscriber_form_interface(is_edit, subscriber)
    
    def create_subscriber_form_interface(self, is_edit, subscriber):
        """إنشاء واجهة نموذج المشترك"""
        main_frame = ttk.Frame(self.form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title_text = "تعديل بيانات المشترك" if is_edit else "إضافة مشترك جديد"
        title_label = ttk.Label(main_frame, text=title_text, 
                               font=('Arial', 16, 'bold'), foreground='#2c3e50')
        title_label.pack(pady=(0, 20))
        
        # إنشاء دفتر الملاحظات للتبويبات
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))
        
        # تبويب البيانات الأساسية
        basic_frame = ttk.Frame(notebook, padding="20")
        notebook.add(basic_frame, text="البيانات الأساسية")
        
        # تبويب القراءات والفواتير
        billing_frame = ttk.Frame(notebook, padding="20")
        notebook.add(billing_frame, text="القراءات والفواتير")
        
        # تبويب معلومات إضافية
        additional_frame = ttk.Frame(notebook, padding="20")
        notebook.add(additional_frame, text="معلومات إضافية")
        
        # إنشاء حقول البيانات الأساسية
        self.create_basic_fields(basic_frame, subscriber)
        
        # إنشاء حقول القراءات والفواتير
        self.create_billing_fields(billing_frame, subscriber)
        
        # إنشاء حقول المعلومات الإضافية
        self.create_additional_fields(additional_frame, subscriber)
        
        # أزرار الحفظ والإلغاء
        buttons_config = [
            ("حفظ", lambda: self.save_subscriber(is_edit, subscriber)),
            ("إلغاء", self.form_window.destroy)
        ]
        
        buttons_frame = WidgetUtils.create_button_frame(main_frame, buttons_config)
        buttons_frame.pack(pady=(10, 0))
    
    def create_basic_fields(self, parent, subscriber):
        """إنشاء حقول البيانات الأساسية"""
        # إطار الحقول
        fields_frame = ttk.Frame(parent)
        fields_frame.pack(fill=tk.X)
        
        # رقم الحساب
        self.account_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "رقم الحساب *:", 0, width=25)
        
        # رقم التركيب
        self.install_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "رقم التركيب:", 1, width=25)
        
        # الرقم التسلسلي
        self.serial_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "الرقم التسلسلي:", 2, width=25)
        
        # الاسم
        self.name_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "اسم المشترك *:", 3, width=40)
        
        # رقم البيت
        self.house_no_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "رقم البيت:", 4, width=15)
        
        # العنوان
        self.address_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "العنوان:", 5, width=50)
        
        # رقم العداد
        self.meter_no_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "رقم العداد:", 6, width=25)
        
        # نوع العداد
        self.meter_phase = WidgetUtils.create_labeled_combobox(
            fields_frame, "نوع العداد:", ["1", "3"], 7, width=10)
        
        # معامل العداد
        self.meter_fact_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "معامل العداد:", 8, width=15)
        
        # اسم العداد
        self.meter_name_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "اسم العداد:", 9, width=30)
        
        # ملء البيانات إذا كان تعديل
        if subscriber:
            self.fill_basic_data(subscriber)
    
    def create_billing_fields(self, parent, subscriber):
        """إنشاء حقول القراءات والفواتير"""
        fields_frame = ttk.Frame(parent)
        fields_frame.pack(fill=tk.X)
        
        # القراءة الأخيرة
        self.last_read_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "القراءة الأخيرة:", 0, width=20)
        
        # تاريخ القراءة الأخيرة
        self.last_date_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "تاريخ القراءة الأخيرة:", 1, width=20)
        
        # القراءة السابقة
        self.prev_read_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "القراءة السابقة:", 2, width=20)
        
        # تاريخ القراءة السابقة
        self.prev_date_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "تاريخ القراءة السابقة:", 3, width=20)
        
        # إيجار العداد
        self.meter_rent_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "إيجار العداد:", 4, width=20)
        
        # إيجار القاطع
        self.cb_rent_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "إيجار القاطع:", 5, width=20)
        
        # رسوم أخرى
        self.other_charge_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "رسوم أخرى:", 6, width=20)
        
        # المبلغ المستحق
        self.outs_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "المبلغ المستحق:", 7, width=20)
        
        # المتأخرات
        self.bkouts_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "المتأخرات:", 8, width=20)
        
        # المبلغ المدفوع
        self.payment_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "المبلغ المدفوع:", 9, width=20)
        
        # تاريخ الدفع
        self.pay_date_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "تاريخ الدفع:", 10, width=20)
        
        # تاريخ الفاتورة
        self.bill_date_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "تاريخ الفاتورة:", 11, width=20)
        
        # ملء البيانات إذا كان تعديل
        if subscriber:
            self.fill_billing_data(subscriber)
    
    def create_additional_fields(self, parent, subscriber):
        """إنشاء حقول المعلومات الإضافية"""
        fields_frame = ttk.Frame(parent)
        fields_frame.pack(fill=tk.X)
        
        # كود البيت
        self.house_cod_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "كود البيت:", 0, width=20)
        
        # حالة الإغلاق
        self.even_close = WidgetUtils.create_labeled_combobox(
            fields_frame, "حالة الحساب:", ["N", "Y"], 1, width=10)
        
        # صرف قديم
        self.old_exch_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "صرف قديم:", 2, width=20)
        
        # علامة عداد سيء
        self.bad_m_flag = WidgetUtils.create_labeled_combobox(
            fields_frame, "علامة عداد سيء:", ["N", "Y"], 3, width=10)
        
        # علامة احتياطي
        self.spare_flag = WidgetUtils.create_labeled_combobox(
            fields_frame, "علامة احتياطي:", ["N", "Y"], 4, width=10)
        
        # حساب الفاتورة
        self.bill_cal_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "حساب الفاتورة:", 5, width=20)
        
        # متوسط الاستهلاك
        self.av_cons_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "متوسط الاستهلاك:", 6, width=20)
        
        # علامة التشغيل
        self.opr_flg = WidgetUtils.create_labeled_combobox(
            fields_frame, "علامة التشغيل:", ["A", "I"], 7, width=10)
        
        # القراءة
        self.reading_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "القراءة:", 8, width=20)
        
        # المنجة
        self.almonege_entry = WidgetUtils.create_labeled_entry(
            fields_frame, "المنجة:", 9, width=20)
        
        # ملء البيانات إذا كان تعديل
        if subscriber:
            self.fill_additional_data(subscriber)
    
    def fill_basic_data(self, subscriber):
        """ملء البيانات الأساسية"""
        self.account_entry.insert(0, subscriber[1] or "")
        self.install_entry.insert(0, subscriber[2] or "")
        self.serial_entry.insert(0, subscriber[3] or "")
        self.name_entry.insert(0, subscriber[4] or "")
        self.house_no_entry.insert(0, subscriber[5] or "")
        self.address_entry.insert(0, subscriber[6] or "")
        self.meter_no_entry.insert(0, subscriber[7] or "")
        self.meter_phase.set(subscriber[8] or "1")
        self.meter_fact_entry.insert(0, subscriber[9] or "")
        self.meter_name_entry.insert(0, subscriber[32] or "")
    
    def fill_billing_data(self, subscriber):
        """ملء بيانات القراءات والفواتير"""
        self.last_read_entry.insert(0, subscriber[10] or "")
        self.last_date_entry.insert(0, subscriber[11] or "")
        self.prev_read_entry.insert(0, subscriber[12] or "")
        self.prev_date_entry.insert(0, subscriber[13] or "")
        self.meter_rent_entry.insert(0, subscriber[14] or "")
        self.cb_rent_entry.insert(0, subscriber[15] or "")
        self.other_charge_entry.insert(0, subscriber[16] or "")
        self.outs_entry.insert(0, subscriber[17] or "")
        self.bkouts_entry.insert(0, subscriber[18] or "")
        self.payment_entry.insert(0, subscriber[21] or "")
        self.pay_date_entry.insert(0, subscriber[22] or "")
        self.bill_date_entry.insert(0, subscriber[23] or "")
    
    def fill_additional_data(self, subscriber):
        """ملء المعلومات الإضافية"""
        self.house_cod_entry.insert(0, subscriber[19] or "")
        self.even_close.set(subscriber[20] or "N")
        self.old_exch_entry.insert(0, subscriber[24] or "")
        self.bad_m_flag.set(subscriber[25] or "N")
        self.spare_flag.set(subscriber[26] or "N")
        self.bill_cal_entry.insert(0, subscriber[27] or "")
        self.av_cons_entry.insert(0, subscriber[28] or "")
        self.opr_flg.set(subscriber[29] or "A")
        self.reading_entry.insert(0, subscriber[30] or "")
        self.almonege_entry.insert(0, subscriber[31] or "")
    
    def validate_form_data(self):
        """التحقق من صحة بيانات النموذج"""
        errors = []
        
        # التحقق من رقم الحساب
        account_no = self.account_entry.get().strip()
        is_valid, error = ValidationUtils.validate_account_number(account_no)
        if not is_valid:
            errors.append(error)
        
        # التحقق من الاسم
        name = self.name_entry.get().strip()
        is_valid, error = ValidationUtils.validate_name(name)
        if not is_valid:
            errors.append(error)
        
        # التحقق من الأرقام
        numeric_fields = [
            (self.meter_fact_entry.get(), "معامل العداد"),
            (self.last_read_entry.get(), "القراءة الأخيرة"),
            (self.prev_read_entry.get(), "القراءة السابقة"),
            (self.meter_rent_entry.get(), "إيجار العداد"),
            (self.cb_rent_entry.get(), "إيجار القاطع"),
            (self.other_charge_entry.get(), "رسوم أخرى"),
            (self.outs_entry.get(), "المبلغ المستحق"),
            (self.bkouts_entry.get(), "المتأخرات"),
            (self.payment_entry.get(), "المبلغ المدفوع"),
            (self.old_exch_entry.get(), "صرف قديم"),
            (self.bill_cal_entry.get(), "حساب الفاتورة"),
            (self.av_cons_entry.get(), "متوسط الاستهلاك"),
            (self.reading_entry.get(), "القراءة"),
            (self.almonege_entry.get(), "المنجة")
        ]
        
        for value, field_name in numeric_fields:
            is_valid, error = ValidationUtils.validate_number(value, field_name)
            if not is_valid:
                errors.append(error)
        
        # التحقق من التواريخ
        date_fields = [
            (self.last_date_entry.get(), "تاريخ القراءة الأخيرة"),
            (self.prev_date_entry.get(), "تاريخ القراءة السابقة"),
            (self.pay_date_entry.get(), "تاريخ الدفع"),
            (self.bill_date_entry.get(), "تاريخ الفاتورة")
        ]
        
        for value, field_name in date_fields:
            is_valid, error = ValidationUtils.validate_date(value)
            if not is_valid:
                errors.append(f"{field_name}: {error}")
        
        return errors
    
    def collect_form_data(self):
        """جمع بيانات النموذج"""
        return {
            'ACCTNO': self.account_entry.get().strip(),
            'INSTALL_NO': self.install_entry.get().strip(),
            'SERIAL': self.serial_entry.get().strip(),
            'NAME_A': self.name_entry.get().strip(),
            'HOUSE_NO': self.house_no_entry.get().strip(),
            'ADRESS': self.address_entry.get().strip(),
            'MATER_NO': self.meter_no_entry.get().strip(),
            'MPHASE': self.meter_phase.get(),
            'METER_FACT': self.meter_fact_entry.get().strip() or None,
            'LAST_READ': self.last_read_entry.get().strip() or None,
            'LAST_DATE': self.last_date_entry.get().strip() or None,
            'PREV_READ': self.prev_read_entry.get().strip() or None,
            'PREV_DATE': self.prev_date_entry.get().strip() or None,
            'METER_RENT': self.meter_rent_entry.get().strip() or None,
            'CB_RENT': self.cb_rent_entry.get().strip() or None,
            'OTHCHARGE': self.other_charge_entry.get().strip() or None,
            'OUTS': self.outs_entry.get().strip() or None,
            'BKOUTS': self.bkouts_entry.get().strip() or None,
            'HOUSE_COD': self.house_cod_entry.get().strip(),
            'EVEN_CLOSE': self.even_close.get(),
            'PAYMENT': self.payment_entry.get().strip() or None,
            'PAY_DATE': self.pay_date_entry.get().strip() or None,
            'BILL_DATE': self.bill_date_entry.get().strip() or None,
            'OLD_EXCH': self.old_exch_entry.get().strip() or None,
            'BAD_M_FLAG': self.bad_m_flag.get(),
            'SPERE_FLAG': self.spare_flag.get(),
            'BILL_CAL': self.bill_cal_entry.get().strip() or None,
            'AV_CONS': self.av_cons_entry.get().strip() or None,
            'OPR_FLG': self.opr_flg.get(),
            'READING': self.reading_entry.get().strip() or None,
            'ALMONEGE': self.almonege_entry.get().strip() or None,
            'METER_NAME': self.meter_name_entry.get().strip()
        }
    
    def save_subscriber(self, is_edit, original_subscriber):
        """حفظ بيانات المشترك"""
        # التحقق من صحة البيانات
        errors = self.validate_form_data()
        if errors:
            error_message = "يرجى تصحيح الأخطاء التالية:\n\n" + "\n".join(errors)
            DialogUtils.show_error("خطأ في البيانات", error_message)
            return
        
        # جمع البيانات
        data = self.collect_form_data()
        
        try:
            conn = sqlite3.connect(self.db.db_path)
            cursor = conn.cursor()
            
            if is_edit:
                # تحديث المشترك الموجود
                self.update_subscriber(cursor, data, original_subscriber[1])
                message = "تم تحديث بيانات المشترك بنجاح"
            else:
                # إضافة مشترك جديد
                self.insert_subscriber(cursor, data)
                message = "تم إضافة المشترك الجديد بنجاح"
            
            conn.commit()
            conn.close()
            
            DialogUtils.show_info("نجح", message)
            self.form_window.destroy()
            
            # تحديث الواجهة الرئيسية إذا كانت متاحة
            if hasattr(self.parent, 'load_data'):
                self.parent.load_data()
            
        except sqlite3.IntegrityError:
            DialogUtils.show_error("خطأ", "رقم الحساب موجود مسبقاً")
        except Exception as e:
            DialogUtils.show_error("خطأ", f"حدث خطأ أثناء الحفظ:\n{str(e)}")
    
    def insert_subscriber(self, cursor, data):
        """إدراج مشترك جديد"""
        columns = ', '.join(data.keys())
        placeholders = ', '.join(['?' for _ in data])
        
        cursor.execute(f"""
            INSERT INTO subscribers ({columns}) 
            VALUES ({placeholders})
        """, list(data.values()))
    
    def update_subscriber(self, cursor, data, account_no):
        """تحديث مشترك موجود"""
        set_clause = ', '.join([f"{key} = ?" for key in data.keys()])
        values = list(data.values()) + [account_no]
        
        cursor.execute(f"""
            UPDATE subscribers 
            SET {set_clause}
            WHERE ACCTNO = ?
        """, values)
