# دليل استخدام قاعدة بيانات فواتير الكهرباء
# Electricity Bills Database Guide

## نظرة عامة
قاعدة بيانات شاملة لإدارة فواتير الكهرباء في محافظة البصرة، تحتوي على معلومات المشتركين والمعاملات المالية.

## محتويات قاعدة البيانات

### الجداول الرئيسية:

#### 1. جدول المشتركين (subscribers)
يحتوي على معلومات جميع مشتركي الكهرباء:
- **ACCTNO**: رقم الحساب (مفتاح أساسي)
- **NAME_A**: اسم المشترك
- **ADRESS**: عنوان المشترك
- **MATER_NO**: رقم العداد
- **OUTS**: المبلغ المستحق
- **PAYMENT**: المبلغ المدفوع
- **LAST_READ**: القراءة الأخيرة
- وحقول أخرى متعددة

#### 2. جدول المعاملات (transactions)
يحتوي على سجل جميع المعاملات:
- **ACCOUNT_NO**: رقم الحساب
- **TRANSACTION_TYPE**: نوع المعاملة
- **OLD_VALUE**: القيمة القديمة
- **NEW_VALUE**: القيمة الجديدة
- **TRANSACTION_DATE**: تاريخ المعاملة
- **NOTES**: ملاحظات

## طرق تشغيل قاعدة البيانات

### 1. التطبيق الرئيسي (واجهة رسومية)
```bash
python main.py
```
أو انقر نقراً مزدوجاً على `run_app.bat`

### 2. عرض معلومات قاعدة البيانات
```bash
python show_db_info.py
```

### 3. أداة إدارة قاعدة البيانات (سطر الأوامر)
```bash
python run_db.py
```

### 4. تشغيل شامل
```bash
run_database.bat
```

## الإحصائيات الحالية

- **عدد المشتركين**: 20 مشترك
- **إجمالي المبالغ المستحقة**: 1,921,089 دينار
- **إجمالي المدفوعات**: 1,472,332 دينار
- **إجمالي المبالغ المتبقية**: 448,757 دينار

## أمثلة على الاستعلامات

### البحث عن مشترك بالاسم:
```sql
SELECT * FROM subscribers WHERE NAME_A LIKE '%أحمد%';
```

### عرض المشتركين ذوي الديون العالية:
```sql
SELECT ACCTNO, NAME_A, (OUTS - PAYMENT) as remaining 
FROM subscribers 
WHERE (OUTS - PAYMENT) > 50000 
ORDER BY remaining DESC;
```

### إحصائيات المدفوعات:
```sql
SELECT 
    COUNT(*) as total_subscribers,
    SUM(OUTS) as total_outstanding,
    SUM(PAYMENT) as total_payments,
    AVG(OUTS - PAYMENT) as avg_remaining
FROM subscribers;
```

## الأدوات المتاحة

### 1. show_db_info.py
عرض معلومات سريعة عن قاعدة البيانات والإحصائيات الأساسية.

### 2. run_db.py
أداة تفاعلية لإدارة قاعدة البيانات مع قائمة خيارات شاملة:
- عرض جميع المشتركين
- البحث بالاسم أو رقم الحساب
- عرض الإحصائيات
- تشغيل استعلامات مخصصة

### 3. db_viewer.py
عارض متقدم لقاعدة البيانات مع واجهة تفاعلية.

## استعلامات مفيدة

### 1. المشتركين الجدد:
```sql
SELECT * FROM subscribers 
WHERE CREATED_DATE >= date('now', '-30 days');
```

### 2. المشتركين بدون مدفوعات:
```sql
SELECT ACCTNO, NAME_A, OUTS 
FROM subscribers 
WHERE PAYMENT = 0 OR PAYMENT IS NULL;
```

### 3. إحصائيات شهرية:
```sql
SELECT 
    strftime('%Y-%m', PAYMENT_DATE) as month,
    COUNT(*) as payments_count,
    SUM(PAYMENT) as total_payments
FROM subscribers 
WHERE PAYMENT_DATE IS NOT NULL
GROUP BY month
ORDER BY month DESC;
```

## النسخ الاحتياطي

لإنشاء نسخة احتياطية من قاعدة البيانات:
```bash
copy electricity_bills.db electricity_bills_backup.db
```

## استيراد البيانات

يمكن استيراد بيانات إضافية باستخدام:
```python
import sqlite3
import pandas as pd

# قراءة من Excel
df = pd.read_excel('new_data.xlsx')

# إدراج في قاعدة البيانات
conn = sqlite3.connect('electricity_bills.db')
df.to_sql('subscribers', conn, if_exists='append', index=False)
conn.close()
```

## الصيانة

### تحسين الأداء:
```sql
VACUUM;
ANALYZE;
```

### إعادة فهرسة:
```sql
REINDEX;
```

## الدعم والمساعدة

للحصول على المساعدة:
1. راجع هذا الدليل
2. استخدم أداة `run_db.py` للاستعلامات التفاعلية
3. تحقق من ملفات السجل للأخطاء

## ملاحظات مهمة

- قاعدة البيانات تستخدم SQLite وهي محمولة وسهلة الاستخدام
- جميع النصوص باللغة العربية مدعومة بالكامل
- يتم حفظ النسخ الاحتياطية تلقائياً عند التعديل
- البيانات الحالية تجريبية ويمكن تعديلها حسب الحاجة

---

**تاريخ آخر تحديث**: يناير 2025
**الإصدار**: 1.0
**المطور**: نظام إدارة فواتير الكهرباء - البصرة
