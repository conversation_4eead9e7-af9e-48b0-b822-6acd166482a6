═══════════════════════════════════════════════════════════════════════════════
                        نظام إدارة فواتير الكهرباء - البصرة
                              تعليمات التشغيل والاستخدام
═══════════════════════════════════════════════════════════════════════════════

📋 محتويات المشروع:
─────────────────────
• main.py - الملف الرئيسي للتطبيق
• database.py - إدارة قاعدة البيانات
• transactions.py - إدارة المعاملات
• reports.py - التقارير والإحصائيات
• subscriber_manager.py - إدارة المشتركين
• utils.py - الأدوات المساعدة
• requirements.txt - متطلبات المشروع
• build_exe.py - أداة بناء الملف التنفيذي
• run_app.bat - ملف تشغيل سريع
• README.md - دليل المشروع

🚀 طرق تشغيل المشروع:
─────────────────────

الطريقة الأولى - التشغيل المباشر:
1. انقر نقراً مزدوجاً على ملف "run_app.bat"
2. سيتم تشغيل النظام تلقائياً

الطريقة الثانية - من سطر الأوامر:
1. افتح موجه الأوامر (Command Prompt)
2. انتقل إلى مجلد المشروع
3. اكتب: python main.py
4. اضغط Enter

الطريقة الثالثة - بناء ملف تنفيذي:
1. اكتب في موجه الأوامر: python build_exe.py
2. اتبع التعليمات
3. شغل الملف التنفيذي من مجلد dist

📦 تثبيت المتطلبات:
──────────────────
إذا لم يعمل المشروع، قم بتثبيت المتطلبات:
pip install -r requirements.txt

أو تثبيت كل مكتبة منفصلة:
pip install pandas openpyxl matplotlib

🎯 ميزات النظام:
──────────────

🔍 البحث والاستعلام:
• بحث بالاسم، رقم الحساب، أو العنوان
• بحث تلقائي أثناء الكتابة
• عرض تفصيلي لكل مشترك

👥 إدارة المشتركين:
• إضافة مشتركين جدد
• تعديل بيانات المشتركين
• حذف المشتركين
• عرض جميع التفاصيل

💼 إدارة المعاملات:
• تغيير الاسم
• إبدال المقياس
• تغيير الصنف
• إغلاق الحساب
• إلغاء المبالغ
• تتبع تاريخ المعاملات

📊 التقارير والإحصائيات:
• تقرير الديون العالية
• تقرير الحسابات الجديدة
• تقرير الاستهلاك
• تقرير المدفوعات
• رسوم بيانية تفاعلية
• إحصائيات شاملة

📤 التصدير والطباعة:
• تصدير البيانات إلى Excel
• طباعة التقارير
• طباعة الفواتير

🎨 الواجهة:
• تصميم احترافي باللغة العربية
• ألوان متناسقة
• سهولة في الاستخدام

💾 قاعدة البيانات:
─────────────────
• يتم إنشاء قاعدة البيانات تلقائياً عند أول تشغيل
• تحتوي على 20 مشترك تجريبي للاختبار
• ملف قاعدة البيانات: electricity_bills.db

📋 البيانات المدعومة:
────────────────────
• رقم الحساب (ACCTNO)
• رقم التركيب (INSTALL_NO)
• الرقم التسلسلي (SERIAL)
• اسم المشترك (NAME_A)
• رقم البيت (HOUSE_NO)
• العنوان (ADRESS)
• رقم العداد (MATER_NO)
• نوع العداد (MPHASE)
• معامل العداد (METER_FACT)
• القراءة الأخيرة (LAST_READ)
• تاريخ القراءة الأخيرة (LAST_DATE)
• القراءة السابقة (PREV_READ)
• تاريخ القراءة السابقة (PREV_DATE)
• إيجار العداد (METER_RENT)
• إيجار القاطع (CB_RENT)
• رسوم أخرى (OTHCHARGE)
• المبلغ المستحق (OUTS)
• المتأخرات (BKOUTS)
• كود البيت (HOUSE_COD)
• حالة الإغلاق (EVEN_CLOSE)
• المبلغ المدفوع (PAYMENT)
• تاريخ الدفع (PAY_DATE)
• تاريخ الفاتورة (BILL_DATE)
• صرف قديم (OLD_EXCH)
• علامة عداد سيء (BAD_M_FLAG)
• علامة احتياطي (SPERE_FLAG)
• حساب الفاتورة (BILL_CAL)
• متوسط الاستهلاك (AV_CONS)
• علامة التشغيل (OPR_FLG)
• القراءة (READING)
• المنجة (ALMONEGE)
• اسم العداد (METER_NAME)

🔧 استكشاف الأخطاء:
─────────────────

إذا لم يعمل المشروع:
1. تأكد من تثبيت Python 3.8 أو أحدث
2. تأكد من تثبيت جميع المتطلبات
3. تأكد من وجود صلاحيات الكتابة في مجلد المشروع
4. تأكد من عدم حجب برنامج مكافح الفيروسات للملفات

إذا ظهرت رسالة خطأ:
1. اقرأ رسالة الخطأ بعناية
2. تأكد من صحة البيانات المدخلة
3. تأكد من وجود ملف قاعدة البيانات
4. أعد تشغيل المشروع

📞 الدعم الفني:
──────────────
• تأكد من قراءة ملف README.md
• راجع رسائل الخطأ في موجه الأوامر
• تأكد من تحديث Python والمكتبات

🎯 نصائح للاستخدام:
─────────────────
• استخدم البحث السريع للعثور على المشتركين
• انقر نقراً مزدوجاً على المشترك لعرض التفاصيل
• استخدم التقارير لمتابعة الإحصائيات
• صدر البيانات بانتظام كنسخة احتياطية
• استخدم المعاملات لتتبع التغييرات

═══════════════════════════════════════════════════════════════════════════════
                              تم تطوير النظام بواسطة
                                  Python & Tkinter
                              مع أطيب التمنيات بالنجاح
═══════════════════════════════════════════════════════════════════════════════
