@echo off
chcp 65001 >nul
title تشغيل قاعدة بيانات فواتير الكهرباء

echo ========================================
echo    تشغيل قاعدة بيانات فواتير الكهرباء
echo ========================================
echo.

echo اختر طريقة التشغيل:
echo 1. تشغيل التطبيق الرئيسي (واجهة رسومية)
echo 2. عرض معلومات قاعدة البيانات
echo 3. أداة إدارة قاعدة البيانات (سطر الأوامر)
echo 4. خروج
echo.

set /p choice="أدخل اختيارك (1-4): "

if "%choice%"=="1" (
    echo تشغيل التطبيق الرئيسي...
    python main.py
) else if "%choice%"=="2" (
    echo عرض معلومات قاعدة البيانات...
    python show_db_info.py
    pause
) else if "%choice%"=="3" (
    echo تشغيل أداة إدارة قاعدة البيانات...
    python run_db.py
) else if "%choice%"=="4" (
    echo وداعاً!
    exit
) else (
    echo خيار غير صحيح!
    pause
    goto :eof
)

pause
