# نظام إدارة فواتير الكهرباء - البصرة

نظام شامل لإدارة فواتير الكهرباء مصمم خصيصاً لمحافظة البصرة، يوفر واجهة احترافية باللغة العربية مع قاعدة بيانات متكاملة.

## المميزات الرئيسية

### 🔍 البحث والاستعلام
- بحث متعدد الأنواع (بالاسم، رقم الحساب، العنوان)
- بحث تلقائي أثناء الكتابة
- عرض تفصيلي لبيانات كل مشترك

### 📊 إدارة المعاملات
- تغيير الاسم
- إبدال المقياس
- تغيير الصنف
- إغلاق الحساب
- معاملات إلغاء المبالغ
- تتبع تاريخ المعاملات

### 📈 التقارير والإحصائيات
- تقرير الديون العالية
- تقرير الحسابات الجديدة
- تقرير الاستهلاك
- تقرير المدفوعات
- رسوم بيانية تفاعلية
- إحصائيات شاملة

### 🛠️ العمليات المتقدمة
- إضافة وتعديل وحذف المشتركين
- فلترة البيانات حسب معايير متعددة
- طباعة الفواتير والتقارير
- تصدير البيانات إلى Excel
- واجهة احترافية مع ثيمات متعددة

## متطلبات النظام

- Windows 10 أو أحدث
- Python 3.8 أو أحدث (للتطوير)
- 4 GB RAM كحد أدنى
- 500 MB مساحة فارغة على القرص الصلب

## طريقة التثبيت والتشغيل

### الطريقة الأولى: تشغيل الملف التنفيذي (الأسهل)

1. قم بتحميل الملف التنفيذي `ElectricityBills.exe`
2. انقر نقراً مزدوجاً على الملف لتشغيله
3. سيتم إنشاء قاعدة البيانات تلقائياً مع بيانات تجريبية

### الطريقة الثانية: تشغيل من الكود المصدري

1. تأكد من تثبيت Python على جهازك
2. افتح موجه الأوامر (Command Prompt) في مجلد المشروع
3. قم بتشغيل الأمر التالي لتثبيت المتطلبات:
   ```
   pip install -r requirements.txt
   ```
4. قم بتشغيل المشروع:
   ```
   python main.py
   ```

### الطريقة الثالثة: استخدام ملف Batch

1. انقر نقراً مزدوجاً على ملف `run_app.bat`
2. سيتم تشغيل المشروع تلقائياً

## بناء ملف تنفيذي جديد

إذا كنت تريد بناء ملف تنفيذي جديد من الكود المصدري:

1. قم بتشغيل:
   ```
   python build_exe.py
   ```
2. اتبع التعليمات التي تظهر على الشاشة
3. سيتم إنشاء الملف التنفيذي في مجلد `dist`

## هيكل قاعدة البيانات

### جدول المشتركين (subscribers)
يحتوي على جميع البيانات المطلوبة:
- ACCTNO: رقم الحساب
- NAME_A: اسم المشترك
- ADRESS: العنوان
- MATER_NO: رقم العداد
- LAST_READ: القراءة الأخيرة
- OUTS: المبلغ المستحق
- PAYMENT: المبلغ المدفوع
- وجميع الحقول الأخرى المطلوبة

### جدول المعاملات (transactions)
يحتوي على سجل جميع المعاملات:
- نوع المعاملة
- القيم القديمة والجديدة
- تاريخ المعاملة
- الملاحظات

## استخدام النظام

### البحث عن المشتركين
1. اختر نوع البحث من القائمة المنسدلة
2. اكتب في حقل البحث
3. ستظهر النتائج تلقائياً

### عرض تفاصيل مشترك
1. انقر نقراً مزدوجاً على المشترك في الجدول
2. ستفتح نافذة جديدة بجميع التفاصيل

### إضافة معاملة
1. اختر المشترك
2. انقر على "المعاملات"
3. انقر على "إضافة معاملة"
4. املأ البيانات المطلوبة

### عرض التقارير
1. انقر على "التقارير"
2. اختر نوع التقرير المطلوب
3. يمكن تصدير التقرير إلى Excel

## البيانات التجريبية

يحتوي النظام على 20 مشترك تجريبي مع بيانات متنوعة لاختبار جميع الميزات.

## الدعم الفني

في حالة مواجهة أي مشاكل:
1. تأكد من تثبيت جميع المتطلبات
2. تأكد من وجود صلاحيات الكتابة في مجلد المشروع
3. تحقق من ملف `electricity_bills.db` في نفس مجلد المشروع

## المطورون

تم تطوير هذا النظام باستخدام:
- Python 3.x
- Tkinter للواجهة الرئيسية
- SQLite لقاعدة البيانات
- Pandas لمعالجة البيانات
- Matplotlib للرسوم البيانية
- ReportLab لإنتاج التقارير

## الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام والتطوير.

---

**ملاحظة**: هذا النظام مصمم خصيصاً لإدارة فواتير الكهرباء في محافظة البصرة ويمكن تخصيصه حسب الاحتياجات المحلية.
