import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
from database import DatabaseManager

class TransactionsManager:
    def __init__(self, parent, db_manager):
        self.parent = parent
        self.db = db_manager
        
    def show_transactions_window(self, account_no=None):
        """عرض نافذة المعاملات"""
        self.trans_window = tk.Toplevel(self.parent)
        self.trans_window.title("إدارة المعاملات")
        self.trans_window.geometry("1000x700")
        self.trans_window.configure(bg='#f0f0f0')
        
        self.current_account = account_no
        self.create_transactions_interface()
        self.load_transactions()
    
    def create_transactions_interface(self):
        """إنشاء واجهة المعاملات"""
        main_frame = ttk.Frame(self.trans_window, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title = ttk.Label(main_frame, text="إدارة المعاملات", 
                         font=('Arial', 16, 'bold'), foreground='#2c3e50')
        title.pack(pady=(0, 20))
        
        # إطار الفلترة
        filter_frame = ttk.LabelFrame(main_frame, text="الفلترة والبحث", padding="10")
        filter_frame.pack(fill=tk.X, pady=(0, 10))
        
        # فلتر رقم الحساب
        ttk.Label(filter_frame, text="رقم الحساب:").grid(row=0, column=0, padx=(0, 5))
        self.account_filter = ttk.Entry(filter_frame, width=15)
        self.account_filter.grid(row=0, column=1, padx=(0, 10))
        if self.current_account:
            self.account_filter.insert(0, self.current_account)
        
        # فلتر نوع المعاملة
        ttk.Label(filter_frame, text="نوع المعاملة:").grid(row=0, column=2, padx=(0, 5))
        self.transaction_type_filter = ttk.Combobox(filter_frame, values=[
            "جميع المعاملات", "تغيير الاسم", "إبدال المقياس", "تغيير الصنف", 
            "إغلاق الحساب", "إلغاء مبلغ", "أخرى"
        ], state="readonly", width=15)
        self.transaction_type_filter.set("جميع المعاملات")
        self.transaction_type_filter.grid(row=0, column=3, padx=(0, 10))
        
        # أزرار الفلترة
        ttk.Button(filter_frame, text="فلترة", command=self.filter_transactions).grid(row=0, column=4, padx=5)
        ttk.Button(filter_frame, text="إعادة تعيين", command=self.reset_filter).grid(row=0, column=5, padx=5)
        
        # جدول المعاملات
        self.create_transactions_tree(main_frame)
        
        # إطار الأزرار
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack(pady=(10, 0))
        
        ttk.Button(buttons_frame, text="إضافة معاملة", 
                  command=self.add_transaction).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="عرض التفاصيل", 
                  command=self.view_transaction_details).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="حذف معاملة", 
                  command=self.delete_transaction).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="تصدير", 
                  command=self.export_transactions).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إغلاق", 
                  command=self.trans_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def create_transactions_tree(self, parent):
        """إنشاء جدول المعاملات"""
        tree_frame = ttk.Frame(parent)
        tree_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        columns = ('ID', 'رقم الحساب', 'نوع المعاملة', 'القيمة القديمة', 
                  'القيمة الجديدة', 'تاريخ المعاملة', 'ملاحظات')
        
        self.trans_tree = ttk.Treeview(tree_frame, columns=columns, show='headings', height=15)
        
        # تعريف الأعمدة
        self.trans_tree.heading('ID', text='ID')
        self.trans_tree.column('ID', width=50, anchor='center')
        
        for col in columns[1:]:
            self.trans_tree.heading(col, text=col)
            self.trans_tree.column(col, width=120, anchor='center')
        
        # شريط التمرير
        scrollbar_v = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.trans_tree.yview)
        scrollbar_h = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.trans_tree.xview)
        self.trans_tree.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        
        # ترتيب العناصر
        self.trans_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        scrollbar_v.grid(row=0, column=1, sticky=(tk.N, tk.S))
        scrollbar_h.grid(row=1, column=0, sticky=(tk.W, tk.E))
        
        tree_frame.columnconfigure(0, weight=1)
        tree_frame.rowconfigure(0, weight=1)
        
        # ربط الأحداث
        self.trans_tree.bind('<Double-1>', self.on_transaction_double_click)
    
    def load_transactions(self):
        """تحميل المعاملات"""
        # مسح البيانات الحالية
        for item in self.trans_tree.get_children():
            self.trans_tree.delete(item)
        
        # جلب المعاملات
        transactions = self.db.get_transactions(self.current_account)
        
        for trans in transactions:
            self.trans_tree.insert('', 'end', values=trans)
    
    def filter_transactions(self):
        """فلترة المعاملات"""
        account_filter = self.account_filter.get().strip()
        type_filter = self.transaction_type_filter.get()
        
        # مسح البيانات الحالية
        for item in self.trans_tree.get_children():
            self.trans_tree.delete(item)
        
        # جلب المعاملات المفلترة
        if account_filter:
            transactions = self.db.get_transactions(account_filter)
        else:
            transactions = self.db.get_transactions()
        
        # فلترة حسب النوع
        if type_filter != "جميع المعاملات":
            transactions = [t for t in transactions if t[2] == type_filter]
        
        for trans in transactions:
            self.trans_tree.insert('', 'end', values=trans)
    
    def reset_filter(self):
        """إعادة تعيين الفلتر"""
        self.account_filter.delete(0, tk.END)
        self.transaction_type_filter.set("جميع المعاملات")
        self.load_transactions()
    
    def add_transaction(self):
        """إضافة معاملة جديدة"""
        self.show_transaction_form()
    
    def show_transaction_form(self, account_no=None, transaction=None):
        """عرض نموذج المعاملة"""
        # إذا لم تكن نافذة المعاملات مفتوحة، افتحها أولاً
        if not hasattr(self, 'trans_window') or not self.trans_window.winfo_exists():
            self.show_transactions_window(account_no)
            return

        form_window = tk.Toplevel(self.trans_window)
        form_window.title("إضافة معاملة جديدة" if not transaction else "تعديل معاملة")
        form_window.geometry("500x400")
        form_window.configure(bg='#f0f0f0')
        
        main_frame = ttk.Frame(form_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # العنوان
        title = ttk.Label(main_frame, text="إضافة معاملة جديدة" if not transaction else "تعديل معاملة",
                         font=('Arial', 14, 'bold'))
        title.pack(pady=(0, 20))
        
        # الحقول
        fields_frame = ttk.Frame(main_frame)
        fields_frame.pack(fill=tk.X, pady=(0, 20))
        
        # رقم الحساب
        ttk.Label(fields_frame, text="رقم الحساب:").grid(row=0, column=0, sticky=tk.W, pady=5)
        account_entry = ttk.Entry(fields_frame, width=30)
        account_entry.grid(row=0, column=1, padx=(10, 0), pady=5)
        if account_no:
            account_entry.insert(0, account_no)
        elif self.current_account:
            account_entry.insert(0, self.current_account)
        
        # نوع المعاملة
        ttk.Label(fields_frame, text="نوع المعاملة:").grid(row=1, column=0, sticky=tk.W, pady=5)
        transaction_type = ttk.Combobox(fields_frame, values=[
            "تغيير الاسم", "إبدال المقياس", "تغيير الصنف", 
            "إغلاق الحساب", "إلغاء مبلغ", "أخرى"
        ], state="readonly", width=27)
        transaction_type.grid(row=1, column=1, padx=(10, 0), pady=5)
        
        # القيمة القديمة
        ttk.Label(fields_frame, text="القيمة القديمة:").grid(row=2, column=0, sticky=tk.W, pady=5)
        old_value_entry = ttk.Entry(fields_frame, width=30)
        old_value_entry.grid(row=2, column=1, padx=(10, 0), pady=5)
        
        # القيمة الجديدة
        ttk.Label(fields_frame, text="القيمة الجديدة:").grid(row=3, column=0, sticky=tk.W, pady=5)
        new_value_entry = ttk.Entry(fields_frame, width=30)
        new_value_entry.grid(row=3, column=1, padx=(10, 0), pady=5)
        
        # الملاحظات
        ttk.Label(fields_frame, text="الملاحظات:").grid(row=4, column=0, sticky=tk.W, pady=5)
        notes_text = tk.Text(fields_frame, width=30, height=4)
        notes_text.grid(row=4, column=1, padx=(10, 0), pady=5)
        
        # أزرار الحفظ والإلغاء
        buttons_frame = ttk.Frame(main_frame)
        buttons_frame.pack()
        
        def save_transaction():
            account = account_entry.get().strip()
            trans_type = transaction_type.get()
            old_value = old_value_entry.get().strip()
            new_value = new_value_entry.get().strip()
            notes = notes_text.get(1.0, tk.END).strip()
            
            if not all([account, trans_type, old_value, new_value]):
                messagebox.showerror("خطأ", "يرجى ملء جميع الحقول المطلوبة")
                return
            
            try:
                self.db.add_transaction(account, trans_type, old_value, new_value, notes)
                messagebox.showinfo("نجح", "تم حفظ المعاملة بنجاح")
                form_window.destroy()
                self.load_transactions()
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء الحفظ:\n{str(e)}")
        
        ttk.Button(buttons_frame, text="حفظ", command=save_transaction).pack(side=tk.LEFT, padx=5)
        ttk.Button(buttons_frame, text="إلغاء", command=form_window.destroy).pack(side=tk.LEFT, padx=5)
    
    def view_transaction_details(self):
        """عرض تفاصيل المعاملة"""
        selection = self.trans_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار معاملة أولاً")
            return
        
        item = self.trans_tree.item(selection[0])
        values = item['values']
        
        # نافذة التفاصيل
        details_window = tk.Toplevel(self.trans_window)
        details_window.title("تفاصيل المعاملة")
        details_window.geometry("400x300")
        details_window.configure(bg='#f0f0f0')
        
        main_frame = ttk.Frame(details_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # عرض التفاصيل
        details = [
            ("رقم المعاملة", values[0]),
            ("رقم الحساب", values[1]),
            ("نوع المعاملة", values[2]),
            ("القيمة القديمة", values[3]),
            ("القيمة الجديدة", values[4]),
            ("تاريخ المعاملة", values[5]),
            ("الملاحظات", values[6])
        ]
        
        for i, (label, value) in enumerate(details):
            ttk.Label(main_frame, text=f"{label}:", font=('Arial', 10, 'bold')).grid(
                row=i, column=0, sticky=tk.W, pady=5)
            ttk.Label(main_frame, text=str(value), font=('Arial', 10)).grid(
                row=i, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        ttk.Button(main_frame, text="إغلاق", command=details_window.destroy).grid(
            row=len(details), column=0, columnspan=2, pady=(20, 0))
    
    def delete_transaction(self):
        """حذف معاملة"""
        selection = self.trans_tree.selection()
        if not selection:
            messagebox.showwarning("تحذير", "يرجى اختيار معاملة أولاً")
            return
        
        if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه المعاملة؟"):
            # هنا يمكن إضافة كود الحذف من قاعدة البيانات
            messagebox.showinfo("تم", "تم حذف المعاملة بنجاح")
            self.load_transactions()
    
    def export_transactions(self):
        """تصدير المعاملات"""
        messagebox.showinfo("قريباً", "هذه الميزة ستكون متاحة قريباً")
    
    def on_transaction_double_click(self, event):
        """عند النقر المزدوج على معاملة"""
        self.view_transaction_details()
