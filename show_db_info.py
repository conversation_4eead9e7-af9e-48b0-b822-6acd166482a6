#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import sys

def show_database_info():
    """عرض معلومات قاعدة البيانات"""
    try:
        conn = sqlite3.connect("electricity_bills.db")
        cursor = conn.cursor()
        
        print("=" * 60)
        print("معلومات قاعدة بيانات فواتير الكهرباء")
        print("=" * 60)
        
        # عرض الجداول
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        
        print("\nالجداول الموجودة:")
        for table in tables:
            print(f"- {table[0]}")
        
        # عرض معلومات جدول المشتركين
        print("\n" + "=" * 40)
        print("معلومات جدول المشتركين:")
        print("=" * 40)
        
        # عدد المشتركين
        cursor.execute("SELECT COUNT(*) FROM subscribers")
        count = cursor.fetchone()[0]
        print(f"عدد المشتركين: {count}")
        
        # عرض أول 5 مشتركين
        cursor.execute("SELECT ACCTNO, NAME_A, ADRESS, OUTS, PAYMENT FROM subscribers LIMIT 5")
        subscribers = cursor.fetchall()
        
        print("\nأول 5 مشتركين:")
        print(f"{'رقم الحساب':<15} {'الاسم':<25} {'العنوان':<30} {'المستحق':<10} {'المدفوع':<10}")
        print("-" * 90)
        
        for sub in subscribers:
            acct = str(sub[0]) if sub[0] else ""
            name = str(sub[1]) if sub[1] else ""
            address = str(sub[2]) if sub[2] else ""
            outs = str(sub[3]) if sub[3] else "0"
            payment = str(sub[4]) if sub[4] else "0"
            
            print(f"{acct:<15} {name:<25} {address:<30} {outs:<10} {payment:<10}")
        
        # إحصائيات مالية
        print("\n" + "=" * 40)
        print("الإحصائيات المالية:")
        print("=" * 40)
        
        cursor.execute("SELECT SUM(OUTS) FROM subscribers WHERE OUTS IS NOT NULL")
        total_outs = cursor.fetchone()[0] or 0
        
        cursor.execute("SELECT SUM(PAYMENT) FROM subscribers WHERE PAYMENT IS NOT NULL")
        total_payments = cursor.fetchone()[0] or 0
        
        remaining = total_outs - total_payments
        
        print(f"إجمالي المبالغ المستحقة: {total_outs:,.0f} دينار")
        print(f"إجمالي المدفوعات: {total_payments:,.0f} دينار")
        print(f"إجمالي المبالغ المتبقية: {remaining:,.0f} دينار")
        
        # معلومات جدول المعاملات
        print("\n" + "=" * 40)
        print("معلومات جدول المعاملات:")
        print("=" * 40)
        
        cursor.execute("SELECT COUNT(*) FROM transactions")
        trans_count = cursor.fetchone()[0]
        print(f"عدد المعاملات: {trans_count}")
        
        if trans_count > 0:
            cursor.execute("SELECT ACCOUNT_NO, TRANSACTION_TYPE, TRANSACTION_DATE FROM transactions LIMIT 3")
            transactions = cursor.fetchall()
            
            print("\nآخر 3 معاملات:")
            print(f"{'رقم الحساب':<15} {'نوع المعاملة':<20} {'التاريخ':<20}")
            print("-" * 55)
            
            for trans in transactions:
                acct = str(trans[0]) if trans[0] else ""
                trans_type = str(trans[1]) if trans[1] else ""
                date = str(trans[2]) if trans[2] else ""
                
                print(f"{acct:<15} {trans_type:<20} {date:<20}")
        
        conn.close()
        print("\n" + "=" * 60)
        print("تم عرض معلومات قاعدة البيانات بنجاح")
        print("=" * 60)
        
    except sqlite3.Error as e:
        print(f"خطأ في قاعدة البيانات: {e}")
    except Exception as e:
        print(f"خطأ عام: {e}")

if __name__ == "__main__":
    show_database_info()
